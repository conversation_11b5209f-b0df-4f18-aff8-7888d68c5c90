import React, { useState } from "react";
import TextArea from "../components/TextArea";
import SelectOption from "../components/SelectOption";
import MessageDisplay from "../components/MessageDisplay";
import { FaTrash, FaSync, <PERSON>a<PERSON><PERSON>board<PERSON>ist, FaReply } from "react-icons/fa";
import "./css/MainForm.css";
// import các biến c<PERSON>u hình
import { API_BASE_URL, CONFIG_VBDLIS_ID } from "../config";
import Snackbar from "../components/Snackbar"; // Import Snackbar
import axios from "axios";

const MainForm: React.FC = () => {
  const [inputText, setInputText] = useState<string>("");
  const [option, setOption] = useState<string>("Tiếp nhận qua VBDLIS");
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [logResults, setLogResults] = useState<any[]>([]);
  const [snackbarVisible, setSnackbarVisible] = useState<boolean>(false);

  const handleChangeText = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIsError(false);
    setInputText(event.target.value);
    setMessage("");
    setSnackbarVisible(false);
    setLogResults([]);
  };

  const handleChangeOption = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setOption(event.target.value);
    setIsError(false);
    setMessage("");
    setSnackbarVisible(false);
    setLogResults([]);
  };

  const callApiVBDLIS = async (modelInput: any, type: any) => {
    try {
      const token = await getTokenVBDLIS();

      let url = "";
      switch (type) {
        case "TiepNhan":
          url = 'https://qng.mplis.gov.vn/dc/api/v2.1/hosomotcua/tiepnhan';
          break;
        case "CapNhatBoSung":
          url = 'https://qng.mplis.gov.vn/dc/api/v2.1/hosomotcua/capnhattrangthaibosunghoso';
          break;
        case "PhanHoiSaiKetQua":
          url = 'https://qng.mplis.gov.vn/dc/api/v2.1/hosomotcua/phanhoihososaiketqua';
          break;
      }

      const response = await axios.post(
        url,
        modelInput,
        {
          headers: {
            Authorization: `Bearer ${token.data}`,
            'Content-Type': 'application/json',
          },
        }
      );
      // Tạo một đối tượng phản hồi có cấu trúc tương tự như `Response`
      return {
        ok: true,
        status: response.status,
        statusText: response.statusText,
        json: async () => response.data,
      };
    } catch (error: any) {
      // Trả về một đối tượng phản hồi lỗi có cấu trúc tương tự như `Response`
      return {
        ok: false,
        status: error.response?.status || 500,
        statusText: error.response?.statusText || 'Internal Server Error',
        json: async () => error.response?.data || { message: 'Unknown Error' },
      };
    }
  };

  const getTokenVBDLIS = async () => {
    try {
      const response = await axios.post('https://qng.mplis.gov.vn/dc/api/v2.1/hosomotcua/gettoken', {
        UserName: 'qng.ketnoimotcua',
        Password: 'Qng!@#456',
      });

      const token = response.data; // Giả sử API trả về token ở đây

      if (token) {
        console.log('Token:', token);
        return token;
      } else {
        console.error('Token not found');
        return null;
      }
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  };

  const fetchToken = async (): Promise<string> => {
    const accessToken = localStorage.getItem("access_token");
    if (!accessToken) {
      console.error("Không tìm thấy accessToken trong localStorage");
      return "";
    }
    return accessToken;
  };

  function extractDocIdFromUrl(url: any) {
    console.log(url);
    if (typeof url !== "string") {
      return url;
    }

    const regex = /DocId=([a-f0-9-]+)/;
    const match = url.match(regex);
    return match ? match[1] : url; // Giữ nguyên url nếu không có DocId
  }

  const handleLogCheck = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    const token = await fetchToken();
    const logResults: any[] = [];  // Cập nhật định nghĩa của logResults
    setLoading(true);
    setIsError(false);
    setMessage("");
    setLogResults([]);  // Reset logResults trước khi lấy dữ liệu mới

    try {
      for (const record of records) {
        try {
          const response = await fetch(
            `${API_BASE_URL}/pa/vbdlis/--log?code=${record}`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            logResults.push(...data);
          }
        } catch (error) {
          console.error(`Failed to fetch log for record ${record}:`, error);
        }
      }

      setMessage("Lấy dữ liệu thành công !");
      setLogResults(logResults);  // Cập nhật logResults với dữ liệu mới
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async (index: number) => {
    // Xác nhận trước khi gửi lại
    const confirmed = window.confirm(
      "Bạn đã kiểm tra lại kiểu timestamp hay chưa? Xác nhận gửi lại?"
    );

    if (!confirmed) return;

    const token = await fetchToken();
    const log = logResults[index];
    const modelInput = JSON.parse(log.modelInput);
    const pathApi = log.pathApi;
    const method = log.method || "POST"; // Default to POST if method is not provided

    // Kiểm tra và chuyển đổi các field timestamp sang UTC
    if (modelInput.NgayHenTraMoi) {
      modelInput.NgayHenTraMoi = convertTimestampToUTC(modelInput.NgayHenTraMoi);
    }

    if (modelInput.NgayChoBoSung) {
      modelInput.NgayChoBoSung = convertTimestampToUTC(modelInput.NgayChoBoSung);
    }

    if (modelInput.NgayTiepNhan) {
      modelInput.NgayTiepNhan = convertTimestampToUTC(modelInput.NgayTiepNhan);
    }

    if (modelInput.NgayHenTra) {
      modelInput.NgayHenTra = convertTimestampToUTC(modelInput.NgayHenTra);
    }

    if (modelInput.ThongTinNguoiNopDon?.NgaySinh) {
      modelInput.ThongTinNguoiNopDon.NgaySinh = convertTimestampToUTC(modelInput.ThongTinNguoiNopDon.NgaySinh);
    }

    // Xử lý DanhSachGiayToDinhKem
    if (Array.isArray(modelInput.DanhSachGiayToDinhKem)) {
      for (let i = 0; i < modelInput.DanhSachGiayToDinhKem.length; i++) {
        const giayTo = modelInput.DanhSachGiayToDinhKem[i];

        // Loại bỏ "null," khỏi TapTin.data và gọi API để lấy dữ liệu base64 và filename mới
        if (giayTo.TapTin?.data) {
          try {
            const ids = giayTo.TapTin.data.replace("null,", "");
            const response = await fetch(
              `https://apiigate.quangngai.gov.vn/fi/file/--base64?ids=${ids}`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.ok) {
              const data = await response.json();
              giayTo.TapTin.data = data[0].base64;
              giayTo.TapTin.name = data[0].filename;
            }
          } catch (error: any) {
            console.error('Error fetching file data:', error);
          }
        }

      }
    }

    setLoading(true);
    setIsError(false);
    setMessage("");

    try {
      let response;

      // Kiểm tra nếu `pathApi` có chứa chuỗi `--receiving-dossier`
      if (pathApi.includes("--receiving-dossier")) {
        response = await callApiVBDLIS(modelInput, "TiepNhan"); // Gọi API tiếp nhận và trả về response
      } else if (pathApi.includes("--update-additional-request")) {
        response = await callApiVBDLIS(modelInput, "CapNhatBoSung");
      } else if (pathApi.includes("--feedback-profile-result-dossier")) {
        response = await callApiVBDLIS(modelInput, "PhanHoiSaiKetQua");
      }
      else {
        // Nếu không có `--receiving-dossier`, thực hiện API call với `fetch`
        response = await fetch(pathApi, {
          method,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(modelInput),
        });
      }

      // Xử lý phản hồi từ cả `fetch` và `callTiepNhanApi`
      if (response.ok) {

        if (pathApi.includes("--receiving-dossier")) {
          const responseJson = await response.json();

          if (responseJson.data === 2 || responseJson.data === 1)
            nextTask(modelInput.SoBienNhan, 2, '', 2, token);
        }

        const data = await response.json(); // hoặc `response.data` nếu là axios
        setMessage(`Thành công: ${JSON.stringify(data)}`);
        setIsError(false);
      } else {
        // Xử lý trường hợp API call thất bại
        setMessage(`Thất bại: ${response.status} - ${response.statusText}`);
        setIsError(true);
      }
    } catch (error: any) {
      // Xử lý lỗi xảy ra khi thực hiện API call
      setMessage(`Lỗi xảy ra: ${error.message}`);
      setIsError(true);
    } finally {
      // Hiển thị snackbar và tắt trạng thái loading
      setSnackbarVisible(true);
      setLoading(false);
    }

  };

  // Hàm chuyển đổi timestamp sang định dạng ngày giờ UTC
  const convertTimestampToUTC = (timestamp: any) => {
    if (!timestamp) return null;

    const date = new Date(timestamp);
    const formattedDate = new Date(
      Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate(),
        date.getUTCHours(),
        date.getUTCMinutes(),
        date.getUTCSeconds(),
        date.getUTCMilliseconds()
      )
    );

    // Chuyển đổi sang định dạng ISO 8601, bỏ qua một số lẻ không cần thiết
    const isoString = formattedDate.toISOString().slice(0, -1);

    return isoString;
  };

  const handleLogResultChange = (index: number, field: string, value: any) => {
    const updatedLogResults = [...logResults];
    updatedLogResults[index] = {
      ...updatedLogResults[index],
      [field]: value,
    };
    setLogResults(updatedLogResults);
  };

  const nextTask = async (code: any, level: any, condition: any, statusVBDLIS: any, token: any) => {
    const apiUrl = `${API_BASE_URL}/pa/vbdlis/--next-task?code=${code}&level=${level}&condition=${condition}&status-vbdlis=${statusVBDLIS}`;
    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  };

  const handleSubmit = async () => {
    if (
      inputText.trim() === "" &&
      option !== "Cập nhật trạng thái hồ sơ VBDLIS"
    ) {
      setMessage("Vui lòng nhập danh sách hồ sơ.");
      setIsError(true);
      return;
    }

    setLoading(true);
    setIsError(false);
    setMessage("");
    setLogResults([]);

    const records = inputText.split(",").map((record) => record.trim());

    switch (option) {
      case "Cập nhật lại file kết quả":
        try {
          const token = await fetchToken();
          const apiUrl = `${API_BASE_URL}/pa/vbdlis/--update-attachment-vbdlislog`;
          const body = { Content: records };

          const response = await fetch(apiUrl, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(body),
          });

          if (response.ok) {
            const data = await response.json();
            const messageString = JSON.stringify(data, null, 2); // Chuyển đổi đối tượng thành chuỗi
            setMessage(`API call successful: ${JSON.stringify(data)}`);
          } else {
            setMessage(
              `API call failed: ${response.status} ${response.statusText}`
            );
            setIsError(true);
          }
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Tiếp nhận qua VBDLIS":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          for (const record of records) {
            const dossierUrl = `${API_BASE_URL}/pa/dossier/${record}/--by-code`;
            const dossierResponse = await fetch(dossierUrl, {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (dossierResponse.ok) {
              const dossierData = await dossierResponse.json();
              const dossierId = dossierData.id;

              const receivingUrl = `${API_BASE_URL}/pa/vbdlis/--receiving-dossier?id=${dossierId}&config-id=${CONFIG_VBDLIS_ID}&is-qni=true`;
              const receivingResponse = await fetch(receivingUrl, {
                method: "POST",
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });

              if (receivingResponse.ok) {
                const receivingData = await receivingResponse.json();
                results.push(
                  `Received dossier ${record} successfully. Dossier id: ${dossierId}. Message: ${receivingData.message}`
                );
              } else {
                results.push(`Failed to receive dossier ${record}.`);
                setIsError(true);
              }
            } else {
              results.push(`Failed to get dossier id for ${record}.`);
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Chuyển sang task đang xử lý":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          for (const record of records) {
            const apiUrl = `${API_BASE_URL}/pa/vbdlis/--next-task?code=${record}&level=2&condition=&status-vbdlis=2`;
            const response = await fetch(apiUrl, {
              method: "PUT",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const responseData = await response.json();
              results.push(
                `Next Task dossier ${record} successfully. Dossier code: ${record}. AffectRow: ${responseData.affectedRows}`
              );
            } else {
              results.push(`Failed to Next Task for code: ${record}.`);
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Chuyển sang bước kết quả khi đang ở bước đang xử lý":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          for (const record of records) {
            const apiUrl = `${API_BASE_URL}/pa/vbdlis/--next-task?code=${record}&level=3&condition=0&status-vbdlis=5`;
            const response = await fetch(apiUrl, {
              method: "PUT",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const responseData = await response.json();
              results.push(
                `Next Task dossier ${record} successfully. Dossier code: ${record}. AffectRow: ${responseData.affectedRows}`
              );
            } else {
              results.push(`Failed to Next Task for code: ${record}.`);
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Chuyển sang bước TH NVTC":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          for (const record of records) {
            const apiUrl = `${API_BASE_URL}/pa/vbdlis/--next-task?code=${record}&level=4&condition=&status-vbdlis=3`;
            const response = await fetch(apiUrl, {
              method: "PUT",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const responseData = await response.json();
              results.push(
                `Next Task dossier ${record} successfully. Dossier code: ${record}. AffectRow: ${responseData.affectedRows}`
              );
            } else {
              results.push(`Failed to Next Task for code: ${record}.`);
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Chuyển sang bước chờ NVTC":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          for (const record of records) {
            const apiUrl = `${API_BASE_URL}/pa/vbdlis/--next-task?code=${record}&level=3&condition=1&status-vbdlis=2`;
            const response = await fetch(apiUrl, {
              method: "PUT",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const responseData = await response.json();
              results.push(
                `Next Task dossier ${record} successfully. Dossier code: ${record}. AffectRow: ${responseData.affectedRows}`
              );
            } else {
              results.push(`Failed to Next Task for code: ${record}.`);
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Cập nhật trạng thái hồ sơ VBDLIS":
        try {
          const token = await fetchToken();
          const statusUrl = `${API_BASE_URL}/pa/vbdlis/--update-status-vbdlis`;
          const body = { Content: records };

          const response = await fetch(statusUrl, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(body),
          });

          if (response.ok) {
            const data = await response.json();
            setMessage(`API call successful: ${JSON.stringify(data)}`);
          } else {
            setMessage(
              `API call failed: ${response.status} ${response.statusText}`
            );
            setIsError(true);
          }
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Đồng bộ bổ sung":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          // Kiểm tra xem records và logResults có phải là mảng và không rỗng
          if (!Array.isArray(records) || records.length === 0) {
            throw new Error("Records is not an array or is empty");
          }

          if (!Array.isArray(logResults) || logResults.length === 0) {
            throw new Error("LogResults is not an array or is empty");
          }

          for (const record of records) {
            const log = logResults.find((item) => item.code === record);

            if (!log) {
              results.push(`Log not found for record ${record}`);
              setIsError(true);
              continue;
            }

            const updatedModelInput = { ...JSON.parse(log.modelInput) };

            let apiUrl = "";
            if (log.pathApi.includes("--approve-request")) {
              apiUrl = `${API_BASE_URL}/pa/vbdlis/${record}/--approve-request`;
              // Xử lý NgayYeuCauDuyet sang giờ UTC
              updatedModelInput.NgayYeuCauDuyet = convertTimestampToUTC(
                updatedModelInput.NgayYeuCauDuyet
              );
              updatedModelInput.GhiChu = `(Đồng bộ lại) ${updatedModelInput.GhiChu}`;
            } else if (log.pathApi.includes("--update-model-input")) {
              apiUrl = `${API_BASE_URL}/pa/vbdlis/${record}/--update-model-input`;
              // Xử lý NgayBatDau, NgayKetThuc sang giờ UTC
              updatedModelInput.NgayBatDau = convertTimestampToUTC(
                updatedModelInput.NgayBatDau
              );
              updatedModelInput.NgayKetThuc = convertTimestampToUTC(
                updatedModelInput.NgayKetThuc
              );
              updatedModelInput.GhiChu = `(Đồng bộ lại) ${updatedModelInput.GhiChu}`;
            } else if (log.pathApi.includes("--additional-request")) {
              apiUrl = `${API_BASE_URL}/pa/vbdlis/${record}/--additional-request`;
              // Xử lý NgayChoBoSung sang giờ UTC
              updatedModelInput.NgayChoBoSung = convertTimestampToUTC(
                updatedModelInput.NgayChoBoSung
              );
              updatedModelInput.GhiChu = `(Đồng bộ lại) ${updatedModelInput.GhiChu}`;
              updatedModelInput.TapTin.LinkFile = extractDocIdFromUrl(
                updatedModelInput.TapTin.LinkFile
              );
            }

            const response = await fetch(apiUrl, {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(updatedModelInput), // Sử dụng toàn bộ updatedModelInput
            });

            if (response.ok) {
              const data = await response.json();
              results.push(
                `Updated ${record} successfully: ${JSON.stringify(data)}`
              );
            } else {
              results.push(
                `Failed to update ${record}: ${response.status} ${response.statusText}`
              );
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      default:
        setMessage(`You selected an unsupported option: ${option}`);
        setIsError(true);
        setLoading(false);
        break;
    }
  };

  return (
    <div className="form-cotainer">
      {option !== "Cập nhật trạng thái hồ sơ VBDLIS" && (
        <TextArea value={inputText} onChange={handleChangeText} />
      )}
      <SelectOption value={option} onChange={handleChangeOption} />
      <div className="processing-content-buttons">
        {option === "Đồng bộ bổ sung" && (
          <button
            className="log-check-button"
            onClick={handleLogCheck}
            disabled={loading || !inputText.trim()}
          >
            <FaClipboardList />
            {loading ? "Đang chạy..." : "Kiểm tra log"}
          </button>
        )}
        <button
          className="submit-button"
          onClick={handleSubmit}
          disabled={
            loading ||
            (!inputText.trim() && option !== "Cập nhật trạng thái hồ sơ VBDLIS")
          }
        >
          <FaSync />
          {loading ? "Đang chạy..." : "Đồng bộ"}
        </button>
      </div>
      <MessageDisplay message={message} isError={isError} />
      {snackbarVisible && (
        <Snackbar
          message={message}
          isError={isError}
          onClose={() => setSnackbarVisible(false)}
        />
      )}
      {logResults.length > 0 && (
        <div className="log-results">
          {logResults.map((result, index) => (
            <div key={index} className="log-item">
              {index === 0 || result.code !== logResults[index - 1].code ? (
                <div className="dossier-code">
                  Mã hồ sơ: {result.code}
                </div>
              ) : null}
              <label>Model Input:</label>
              <textarea
                value={result.modelInput}
                onChange={(e) =>
                  handleLogResultChange(index, "modelInput", e.target.value)
                }
              />
              <label>Result Message:</label>
              <input
                value={result.resultMessage}
                onChange={(e) =>
                  handleLogResultChange(index, "resultMessage", e.target.value)
                }
              />
              <label>Result:</label>
              <input
                type="text"
                value={result.result}
                onChange={(e) =>
                  handleLogResultChange(index, "result", e.target.value)
                }
              />
              <label>Create Date:</label>
              <input
                type="text"
                value={result.createDate}
                onChange={(e) =>
                  handleLogResultChange(index, "createDate", e.target.value)
                }
              />
              <label>Path Api:</label>
              <input
                type="text"
                value={
                  result.pathApi.includes("PUT")
                    ? `https://apiigate.quangngai.gov.vn/pa/${result.pathApi.trim().replace(/\s+/g, "")}`
                    : result.pathApi.trim().replace(/\s+/g, "")
                }
                onChange={(e) => {
                  const cleanedValue = e.target.value.trim().replace(/\s+/g, ""); // Loại bỏ khoảng trắng
                  const newValue = cleanedValue.includes("PUT")
                    ? `https://apiigate.quangngai.gov.vn/pa/${cleanedValue}`
                    : cleanedValue;

                  handleLogResultChange(index, "pathApi", newValue);
                }}
              />
              <label>Method:</label>
              <select
                value={result.pathApi.includes("PUT") ? "PUT" : result.method || ""}
                onChange={(e) =>
                  handleLogResultChange(index, "method", e.target.value)
                }
              >
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="GET">GET</option>
                <option value="DELETE">DELETE</option>
              </select>
              <div>

              </div>
              <div className="processing-content-buttons">
                <button onClick={() => handleResend(index)}><FaReply />{loading ? "Đang chạy..." : "Gọi lại"}</button>
              </div>
            </div>
          ))}
        </div>
      )}

    </div>
  );
};

export default MainForm;