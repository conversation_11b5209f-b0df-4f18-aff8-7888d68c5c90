/* T<PERSON>ng thể menu */
.menu {
  width: 200px;
  background-color: #f8f9fa;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
}

/* Nút menu chính */
.menu-header {
  margin-bottom: 10px;
}

.menu-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  text-align: left;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-button.active {
  background-color: #0056b3;
}

.menu-button:hover {
  background-color: #0056b3;
}

.menu-icon {
  margin-right: 10px;
}

.dropdown-icon {
  margin-left: auto;
  transition: transform 0.3s;
}

.dropdown-icon.rotate {
  transform: rotate(180deg);
}

/* Submenu */
.submenu {
  margin-top: 10px;
  padding-left: 20px;
}

.submenu-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px;
  background-color: #e2e6ea;
  color: black;
  border: none;
  border-radius: 5px;
  text-align: left;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: 5px;
}

.submenu-button.selected {
  background-color: #a6acb3;
}

.submenu-button:hover {
  background-color: #dae0e5;
}

.submenu-icon {
  margin-right: 10px;
}
