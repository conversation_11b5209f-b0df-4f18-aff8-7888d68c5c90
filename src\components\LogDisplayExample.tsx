import React from "react";
import { FaReply } from "react-icons/fa";
import "../pages/css/FormDVCLT.css";

interface ExampleLogResult {
  id: string;
  api: string;
  responseBody: any;
  requestBody: any;
  nationCode: string;
}

const LogDisplayExample: React.FC = () => {
  // Example data to demonstrate the new display format
  const exampleLogs: ExampleLogResult[] = [
    {
      id: "1",
      api: "/api/dvclt/update",
      nationCode: "ABC123",
      responseBody: { status: "success", message: "Cập nhật thành công" },
      requestBody: { trangThai: 1, ghiChu: "Đã xử lý" }
    },
    {
      id: "2", 
      api: "/api/dvclt/update",
      nationCode: "XYZ789",
      responseBody: { status: "success", message: "Cập nhật thành công" },
      requestBody: { trangThai: 2, ghi<PERSON>hu: "Đang xử lý" }
    },
    {
      id: "3",
      api: "/api/dvclt/update", 
      nationCode: "DEF456",
      responseBody: { status: "error", message: "Lỗi kết nối" },
      requestBody: { trangThai: 0, ghiChu: "Chờ xử lý" }
    }
  ];

  return (
    <div className="log-results">
      <h3>Ví dụ hiển thị log mới ({exampleLogs.length} bản ghi):</h3>
      <p style={{ 
        background: '#f0f9ff', 
        padding: '1rem', 
        borderRadius: '8px', 
        border: '1px solid #0ea5e9',
        marginBottom: '1rem',
        fontSize: '0.9rem'
      }}>
        <strong>🎯 Cải tiến:</strong> Bây giờ mỗi log sẽ hiển thị rõ ràng code tương ứng trong tiêu đề, 
        giúp phân biệt dễ dàng khi có nhiều code khác nhau.
      </p>
      
      {exampleLogs.map((log, index) => (
        <div key={index} className="log-item">
          <div className="log-header">
            <h4>
              Log {index + 1} - Code: {log.nationCode}
            </h4>
            <button
              className="btn-resend"
              title="Gọi lại"
              disabled
            >
              <FaReply />
              Gọi lại
            </button>
          </div>
          
          <div className="log-content">
            <div className="log-field">
              <label>API:</label>
              <div className="log-value">
                <pre>{log.api}</pre>
              </div>
            </div>
            
            <div className="log-field">
              <label>Response:</label>
              <div className="log-value">
                <pre>{JSON.stringify(log.responseBody, null, 2)}</pre>
              </div>
            </div>
            
            <div className="log-field">
              <label>Request Body:</label>
              <div className="log-value">
                <pre>{JSON.stringify(log.requestBody, null, 2)}</pre>
              </div>
            </div>
          </div>
        </div>
      ))}
      
      <div style={{
        background: '#f8fafc',
        padding: '1rem',
        borderRadius: '8px',
        border: '1px solid #e2e8f0',
        marginTop: '1rem'
      }}>
        <h4 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>
          📋 Các cải tiến đã thực hiện:
        </h4>
        <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#6b7280' }}>
          <li>Hiển thị code trong tiêu đề mỗi log: "Log 1 - Code: ABC123"</li>
          <li>Thông báo thành công/thất bại khi gửi lại cũng hiển thị code</li>
          <li>Modal hiển thị kết quả cũng bao gồm thông tin code</li>
          <li>Styling được cải thiện với gradient và underline cho tiêu đề</li>
        </ul>
      </div>
    </div>
  );
};

export default LogDisplayExample;
