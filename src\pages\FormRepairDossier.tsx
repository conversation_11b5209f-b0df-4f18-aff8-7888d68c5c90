import React, { useState } from "react";
import TextArea from "../components/TextArea";
import SelectOptionStatusDossier from "../components/SelectOptionStatusDossier";
import Button from "../components/Button";
import MessageDisplay from "../components/MessageDisplay";
// import các biến cấu hình
import { API_BASE_URL } from '../config';
import { FaCalendarCheck, FaChevronDown } from 'react-icons/fa';

const FormRepairDossier: React.FC = () => {
  const [inputText, setInputText] = useState<string>("");
  const [option, setOption] = useState<string>("Rút hồ sơ");
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [statusInfo, setStatusInfo] = useState<string[]>([]);
  const [statusLoading, setStatusLoading] = useState<boolean>(false);
  const [statusError, setStatusError] = useState<string | null>(null);

  const handleChangeText = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIsError(false);
    setInputText(event.target.value);
    setStatusInfo([]);
    setMessage("");
  };

  const handleChangeOption = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setOption(event.target.value);
  };

  const fetchToken = async (): Promise<string> => {
    const accessToken = localStorage.getItem("access_token");
    if (!accessToken) {
      console.error("Không tìm thấy accessToken trong localStorage");
      throw new Error("Không tìm thấy accessToken trong localStorage");
    }

    return accessToken;
  };


  const updateDossierStatus = async (dossierCode: string, option: string) => {
    const token = await fetchToken();
    let body: any = {};

    switch (option) {
      case "Rút hồ sơ":
        body = {
          dossierStatus: 6,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "6147e4c4ebfba925f1e89bf0",
            name: [
              {
                languageId: 228,
                name: "Đã rút hồ sơ",
                description: "Đã rút hồ sơ",
              },
              {
                languageId: 46,
                name: "Has Withdrawn",
                description: "Has Withdrawn",
              },
            ],
          },
          dossierMenuTaskRemind: {
            id: "6151c728ba2a04299f949871",
            name: [
              { languageId: 228, name: "HS đã rút", description: "HS đã rút" },
            ],
          },
        };
        break;

      case "Trả kết quả":
        body = {
          dossierStatus: 5,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "60ebf17309cbf91d41f87f8e",
            name: [
              {
                languageId: 228,
                name: "Đã trả kết quả",
                description: "Đã trả kết quả",
              },
            ],
          },
          dossierMenuTaskRemind: {
            id: "60f52f0109cbf91d41f88839",
            name: [
              { languageId: 228, name: "Đã trả kết quả" },
              { languageId: 46, name: "Result returned" },
            ],
            viStatus: "Đã trả kết quả",
          },
        };
        break;

      case "Đang xử lý":
        body = {
          dossierStatus: 2,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "61c2949dc1e3d80b8fbdca3c",
            name: [
              {
                languageId: 228,
                name: "Đang xử lý",
                description: "Đang xử lý",
              },
            ],
          },
          dossierMenuTaskRemind: {
            id: "61c29504c1e3d80b8fbdca3f",
            name: [
              { languageId: 228, name: "Xử lý hồ sơ" },
              { languageId: 46, name: "Processing Dossier" },
            ],
            viStatus: "Xử lý hồ sơ",
          },
        };
        break;

      case "Yêu cầu bổ sung":
        body = {
          dossierStatus: 1,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "60ebf03109cbf91d41f87f8b",
            name: [
              {
                languageId: 228,
                name: "Yêu cầu bổ sung giấy tờ",
                description: "Yêu cầu bổ sung giấy tờ",
              },
            ],
          },
          dossierMenuTaskRemind: {
            id: "60f52e6a09cbf91d41f88836",
            name: [
              { languageId: 228, name: "Yêu cầu bổ sung" },
              { languageId: 46, name: "Additional requirements" },
            ],
            viStatus: "Yêu cầu bổ sung",
          },
        };
        break;

      case "Dừng xử lý":
        body = {
          dossierStatus: 12,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "61ee30eada2d36b037e00005",
            name: [
              {
                languageId: 228,
                name: "Dừng xử lý",
                description: "Dừng xử lý",
              },
              { languageId: 46, name: "Stop processing" },
            ],
          },
          dossierMenuTaskRemind: {
            id: "61ee30eada2d36b037e00005",
            name: [
              { languageId: 228, name: "Dừng xử lý" },
              { languageId: 46, name: "Stop processing" },
            ],
            viStatus: "Dừng xử lý",
          },
        };
        break;

      case "Mới đăng ký":
        body = {
          dossierStatus: 0,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "60e409823dfc9609723e493c",
            name: [
              {
                "languageId": 228,
                "name": "Mới đăng ký",
                "description": "Mới đăng ký"
              }
            ]
          },

          dossierMenuTaskRemind: {
            id: "60f52e0d09cbf91d41f88834",
            name: [
              {
                languageId: 228,
                name: "Mới đăng ký"
              },
              {
                languageId: 46,
                name: "Just registered"
              }
            ],
            viStatus: "Mới đăng ký"
          }
        };
        break;

      case "Đã xử lý xong":
        body = {
          dossierStatus: 4,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "6206911d677d1a2e1527bcb3",
            name: [
              {
                "languageId": 228,
                "name": "Đã xử lý xong",
                "description": "Đã xử lý xong"
              }
            ]
          },

          dossierMenuTaskRemind: {
            id: "621de31f63bc5e78e3944502",
            name: [
              {
                "languageId": 228,
                "name": "Hồ sơ chờ trả kết quả"
              },
              {
                "languageId": 46,
                "name": "Result returned"
              }
            ],
            "viStatus": "Hồ sơ chờ trả kết quả"
          }
        };
        break;

      case "Đang tạm dừng":
        body = {
          dossierStatus: 3,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "60ed1b7c09cbf91d41f87fa0",
            name: [
              {
                "languageId": 228,
                "name": "Đang tạm dừng",
                "description": "Đang tạm dừng"
              }
            ]
          },

          dossierMenuTaskRemind: {
            id: "60f52f5209cbf91d41f8883b",
            name: [
              {
                languageId: 228,
                name: "Tạm dừng"
              },
              {
                languageId: 46,
                name: "Pausing"
              }
            ],
            viStatus: "Tạm dừng"
          }
        };
        break;

      case "Từ chối từ chuyên ngành":
        body = {
          dossierStatus: 19,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "691e4a0967411b0b0d000011",
            name: [
              {
                languageId: 228,
                name: "Từ chối từ chuyên ngành",
                description: "Từ chối từ chuyên ngành",
              },
            ],
          },
          dossierMenuTaskRemind: {
            id: "691e4a0967411b0b0d000011",
            name: [
              { languageId: 228, name: "Từ chối từ chuyên ngành" },
              { languageId: 46, name: "Processing Dossier" },
            ],
            viStatus: "Từ chối từ chuyên ngành",
          },
        };
        break;

      case "Chờ phê duyệt dừng xử lý":
        body = {
          dossierStatus: 3,
          comment: "<p>Tool đồng bộ lại</p>",
          dossierTaskStatus: {
            id: "61ee30eada2d36b037e00004",
            name: [
              {
                "languageId": 228,
                "name": "Chờ phê duyệt dừng xử lý",
                "description": "Chờ phê duyệt dừng xử lý"
              }
            ]
          },

          dossierMenuTaskRemind: {
            id: "61ee30eada2d36b037e00004",
            name: [
              {
                languageId: 228,
                name: "Chờ phê duyệt dừng xử lý"
              },
              {
                languageId: 46,
                name: "Pending approval stop processing"
              }
            ],
            viStatus: "Chờ phê duyệt dừng xử lý"
          }
        };
        break;

      case "Hủy hồ sơ (Đổi trạng thái)":
        body = {
          dossierStatus: 6,
          comment: `<p>Tool đồng bộ lại</p> ${dossierCode} đã hủy trên tool`,
          dossierTaskStatus: {
            id: "60ed1a5909cbf91d41f87f9f",
            name: [
              {
                "languageId": 228,
                "name": "Hồ sơ đã hủy",
                "description": "Hồ sơ đã hủy"
              }
            ]
          },

          dossierMenuTaskRemind: {
            id: "60f52f3109cbf91d41f8883a",
            name: [
              {
                languageId: 228,
                name: "Đã hủy"
              },
              {
                languageId: 46,
                name: "Pending approval stop processing"
              }
            ],
            viStatus: "Đã hủy"
          }
        };
        break;

      case "Trả hồ sơ về chờ tiếp nhận":
        body = {};
        break;
      case "Hủy hồ sơ":
        body = {};
        break;
      case "Xóa hồ sơ":
        body = {};
        break;
      default:
        setIsError(true);
        setMessage(`Unsupported option: ${option}`);
        setLoading(false);
        return;
    }

    try {
      const dossierUrl = `${API_BASE_URL}/pa/dossier/${dossierCode}/--by-code`;
      const dossierResponse = await fetch(dossierUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!dossierResponse.ok) {
        throw new Error(
          `Failed to retrieve dossier information for ${dossierCode}`
        );
      }

      const dossierData = await dossierResponse.json();
      const dossierId = dossierData.id;

      // trả hồ sơ về chờ tiếp nhận, ngoài luồng
      var statusUrl = "";
      if (option === "Trả hồ sơ về chờ tiếp nhận") {
        statusUrl = `${API_BASE_URL}/pa/dossier/${dossierId}/--return-accept`;
      }
      else if (option === "Hủy hồ sơ") {
        statusUrl = `${API_BASE_URL}/pa/dossier/${dossierId}/?is-canceled=1`;
      }
      else if (option === "Xóa hồ sơ") {
        statusUrl = `${API_BASE_URL}/pa/dossier/${dossierId}/?is-canceled=0`;
      }
      else {
        statusUrl = `${API_BASE_URL}/pa/dossier/${dossierId}/status`;
      }

      const statusResponse = await fetch(statusUrl, {
        method: option === "Xóa hồ sơ" ? "DELETE" : "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(body),
      });

      if (!statusResponse.ok) {
        throw new Error(`Failed to update status for dossier ${dossierCode}`);
      }

      var response = await statusResponse.json();
      return `Dossier ${dossierCode} message: ${response.affectedRows === 0 ? response.message : "Thành công!"
        }`;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Error updating dossier status: ${error.message}`);
      } else {
        // Handle unknown error type
        console.error("Unknown error:", error);
        throw new Error("An unknown error occurred while updating dossier status.");
      }
    }
  };

  const handleCheckStatus = async () => {
    const token = await fetchToken();
    setStatusLoading(true);
    setStatusError(null);
    setMessage("");
    try {
      // Tách các mã hồ sơ bằng dấu phẩy
      const dossierCodes = inputText.split(",").map((code) => code.trim());

      // Duyệt qua từng mã hồ sơ
      const statusInfos: string[] = [];
      for (const dossierCode of dossierCodes) {
        const dossierUrl = `${API_BASE_URL}/pa/dossier/${dossierCode}/--by-code`;
        const dossierResponse = await fetch(dossierUrl, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        console.log(dossierResponse.status);
        if (dossierResponse.ok) {
          const dossierData = await dossierResponse.json();
          statusInfos.push(
            `${dossierCode}: ${dossierData?.dossierTaskStatus?.name}`
          );
        } else if (dossierResponse.status !== 200) {
          const dossierData = await dossierResponse.json();
          statusInfos.push(`${dossierCode}: ${dossierData?.message}`);
        } else {
          throw new Error(
            `Failed to retrieve dossier information for ${dossierCode}`
          );
        }
      }

      setStatusInfo(statusInfos);
    } catch (error: any) {
      setStatusError(`Error checking dossier status: ${error.message}`);
    } finally {
      setStatusLoading(false);
    }
  };

  const handleSubmit = async () => {
    setStatusInfo([]);
    setLoading(true);
    setIsError(false);
    setMessage("");

    const records = inputText.split(",").map((record) => record.trim());
    const results: string[] = [];

    for (const record of records) {
      try {
        const result = await updateDossierStatus(record, option);
        if (typeof result === 'string') { // Check if result is a string
          results.push(result);
        } else {
          // Handle unexpected result type if needed
          console.error('Unexpected result type:', typeof result);
        }
      } catch (error: any) {
        setIsError(true);
        results.push(error.message);
      }
    }

    setLoading(false);
    setMessage(results.join("\n"));
  };

  return (
    <div className="form-cotainer">
      <TextArea value={inputText} onChange={handleChangeText} />
      <SelectOptionStatusDossier value={option} onChange={handleChangeOption} />

      <div className="processing-content-buttons">
        <Button
          onClick={handleCheckStatus}
          disabled={statusLoading || !inputText.trim()}
        >
          <FaCalendarCheck />
          {statusLoading ? "Đang kiểm tra..." : "Kiểm tra trạng thái"}
        </Button>
        <Button onClick={handleSubmit} disabled={loading || !inputText.trim()}>
          <FaChevronDown />
          {loading ? "Đang chạy..." : "Cập nhật"}
        </Button>
      </div>

      <MessageDisplay message={statusError || ""} isError={isError} />
      {statusInfo.length > 0 && (
        <div>
          {statusInfo.map((status, index) => (
            <MessageDisplay message={status} isError={isError} />
          ))}
        </div>
      )}

      <MessageDisplay message={message} isError={isError} />
    </div>
  );
};

export default FormRepairDossier;
