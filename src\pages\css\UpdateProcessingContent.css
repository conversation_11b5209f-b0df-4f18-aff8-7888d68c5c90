.processing-content {
  margin-bottom: 20px; /* <PERSON><PERSON> tạo <PERSON> cách gi<PERSON>a các mục processing-content */
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 10px;
}

.processing-content label {
  font-weight: bold;
  margin-right: 5px;
}

.processing-content .dossier-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.processing-content .dossier-code {
  color: orange;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.processing-content div {
  text-align: left;
  margin-bottom: 5px;
}


.btn-update {
  background-color: #4caf50;
  color: white;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  margin-right: 10px;
}

.btn-delete {
  background-color: #f44336;
  color: white;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
}

.processing-content-buttons button svg {
  margin-right: 5px; /* K<PERSON>ảng cách giữa icon và chữ */
}