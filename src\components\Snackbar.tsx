import React, { useEffect } from "react";
import "./Snackbar.css";

interface SnackbarProps {
  message: string;
  isError: boolean;
  onClose: () => void;
}

const Snackbar: React.FC<SnackbarProps> = ({ message, isError, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);

    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <div className={`snackbar ${isError ? "error" : "success"}`}>
      {message}
      <button onClick={onClose} className="snackbar-close">
        X
      </button>
    </div>
  );
};

export default Snackbar;
