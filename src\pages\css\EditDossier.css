.error {
  color: red;
  margin-top: 10px;
}

.dossier-detail {
  margin-bottom: 20px; /* <PERSON><PERSON> c<PERSON>ch gi<PERSON>a các mục processing-content */
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 10px;
}

.dossier-detail label {
  font-weight: bold;
  margin-right: 5px;
}

.dossier-detail .dossier-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.dossier-detail .dossier-code {
  color: orange;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.dossier-detail div {
  text-align: left;
  margin-bottom: 5px;
}

.file-item {
  display: flex;
  align-items: center;
}

.remove-button {
  width: 54px;
  margin-left: 10px;
  background: red;
  border: none;
  cursor: pointer;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.new-upload {
  background-color: #008cff;
  color: white;
}

.additional-upload {
  background-color: #28a745;
  color: white;
}

.file-attachment-section {
  border: 1px solid #ccc;
  padding: 15px;
  margin: 15px 0;
  background-color: #cccccc;
  border-radius: 5px;
}

.file-attachment-section label {
  font-weight: bold;
  display: block;
  margin-bottom: 10px;
}

.file-attachment-section ul {
  list-style-type: none;
  padding: 0;
}

.file-attachment-section li {
  margin-bottom: 5px;
}

.file-item {
  display: flex;
  align-items: center;
}


.detail-content-buttons {
  display: flex;
  justify-content: center; /* Căn giữa các nút */
  gap: 10px; /* Khoảng cách giữa các nút */
  margin-top: 10px;
  width: 100%; /* Đảm bảo container chiếm toàn bộ chiều rộng */
}

.detail-content-buttons button {
  padding: 5px 10px; /* Giảm padding để nút nhỏ hơn */
  font-size: 14px; /* Giảm kích thước font */
  min-width: 75px; /* Đặt kích thước cố định nếu cần */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px; /* Thiết lập chiều rộng cho nút */
  height: 41px;
}
