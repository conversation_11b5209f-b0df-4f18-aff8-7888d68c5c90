import React from "react";

interface SelectOptionProps {
  value: string;
  onChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
}

const SelectOptionStatusDossier: React.FC<SelectOptionProps> = ({
  value,
  onChange,
}) => {
  return (
    <select value={value} onChange={onChange}>
      <option value="<PERSON><PERSON><PERSON> hồ sơ"><PERSON><PERSON><PERSON>ồ s<PERSON></option>
      <option value="Trả kết quả"><PERSON><PERSON><PERSON> kết quả</option>
      <option value="Đang xử lý"><PERSON><PERSON> xử lý</option>
      <option value="<PERSON><PERSON><PERSON> cầu bổ sung"><PERSON><PERSON><PERSON> c<PERSON><PERSON> bổ sung</option>
      <option value="Dừng xử lý">D<PERSON>ng xử lý</option>
      <option value="Mới đăng ký">Mới đăng ký</option>
      <option value="Tr<PERSON> hồ sơ về chờ tiếp nhận">
        <PERSON><PERSON><PERSON> hồ sơ về chờ tiếp nhận
      </option>
      <option value="<PERSON><PERSON><PERSON> hồ sơ">
        <PERSON><PERSON><PERSON> hồ sơ
      </option>
      <option value="<PERSON><PERSON><PERSON> hồ sơ (<PERSON><PERSON><PERSON> trạng thái)">
        <PERSON><PERSON><PERSON> hồ sơ (<PERSON><PERSON><PERSON> trạng thái)
      </option>
      <option value="Đã xử lý xong">
        Đã xử lý xong
      </option>
      <option value="Đang tạm dừng">
        Đang tạm dừng
      </option>
      <option value="Chờ phê duyệt dừng xử lý">
        Chờ phê duyệt dừng xử lý
      </option>
      <option value="Xóa hồ sơ">
        Xóa hồ sơ
      </option>
      <option value="Từ chối từ chuyên ngành">
      Từ chối từ chuyên ngành
      </option>
    </select>
  );
};

export default SelectOptionStatusDossier;
