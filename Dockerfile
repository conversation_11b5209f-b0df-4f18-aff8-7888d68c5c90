# Sử dụng hình ảnh node chính thức làm hình ảnh cơ sở
FROM node:14 AS build

# Thi<PERSON>t lập thư mục làm việc
WORKDIR /app

# Sao chép package.json và package-lock.json vào thư mục làm việc
COPY package*.json ./

# Cài đặt các phụ thuộc
RUN npm install

# Sao chép phần còn lại của mã ứng dụng vào thư mục làm việc
COPY . .

# Build ứng dụng React
RUN npm run build

# Sử dụng hình ảnh nginx chính thức làm hình ảnh cơ sở cho môi trường sản xuất
FROM nginx:stable

# Sao chép các tệp React đã build vào thư mục html của nginx
COPY --from=build /app/build /usr/share/nginx/html

# Sao chép cấu hình nginx tùy chỉnh
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Mở cổng nơi nginx đang chạy
EXPOSE 80

# Khởi động nginx
CMD ["nginx", "-g", "daemon off;"]
