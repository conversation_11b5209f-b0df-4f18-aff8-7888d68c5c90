# NGSP API SSL Configuration Guide

## Tổng quan
Hệ thống đã được cấu hình để sử dụng HTTPS cho tất cả các API calls đến `api.ngsp.gov.vn`, với fallback về HTTP khi cần thiết.

## Thay đổi đã thực hiện

### 1. Cấu hình trong `src/config.js`
```javascript
NGSP: {
    endpoint: "https://api.ngsp.gov.vn/token", // Primary HTTPS endpoint
    endpointHttp: "http://api.ngsp.gov.vn/token", // HTTP fallback for SSL bypass
    username: "Y21dLthdMKXcBfhDvEpRRfLb30sa",
    password: "D9nmHHleKixNwrhp4BhFM57uSIwa",
    // SSL bypass configuration
    sslBypass: true,
    timeout: 15000
}
```

### 2. HTTP Client Helper (`src/utils/httpClient.js`)
- Tạo axios client với cấu hình SSL bypass
- Tự động fallback từ HTTPS sang HTTP
- Logging chi tiết cho debugging
- Error handling cho SSL certificate issues

### 3. Cập nhật FormDVCLT.tsx
- Sử dụng helper functions từ `httpClient.js`
- Improved error handling cho SSL issues
- Automatic HTTPS/HTTP fallback

## Cách xử lý SSL Certificate Issues

### Trong Browser Development:

1. **Accept Certificate Manual:**
   - Mở https://api.ngsp.gov.vn/token trong browser
   - Click "Advanced" → "Proceed to api.ngsp.gov.vn (unsafe)"

2. **Chrome Flags:**
   - Vào `chrome://flags/#allow-insecure-localhost`
   - Set thành "Enabled"

3. **Chrome Command Line:**
   ```bash
   chrome.exe --ignore-certificate-errors --ignore-ssl-errors --allow-running-insecure-content
   ```

4. **Add to Trusted Sites:**
   - Windows: Internet Options → Security → Trusted Sites
   - Add `*.ngsp.gov.vn`

### Trong Production:

1. **Proxy Server:** Sử dụng proxy server để handle SSL
2. **Certificate Installation:** Install proper SSL certificates
3. **VPN:** Có thể cần VPN để access api.ngsp.gov.vn

## API Endpoints được cập nhật:

### Token Endpoint:
- **HTTPS:** `https://api.ngsp.gov.vn/token`
- **HTTP Fallback:** `http://api.ngsp.gov.vn/token`

### API Endpoint:
- **HTTPS:** `https://api.ngsp.gov.vn/Lienthonghotich/1.0/nhanHoSoDKHT`
- **HTTP Fallback:** `http://api.ngsp.gov.vn/Lienthonghotich/1.0/nhanHoSoDKHT`

## Cách sử dụng:

### Import helper functions:
```javascript
import { callNGSPAPI, fetchNGSPToken, openNGSPCertificateAcceptance } from "../utils/httpClient";
```

### Fetch token:
```javascript
const token = await fetchNGSPToken(username, password);
```

### Make API call:
```javascript
const response = await callNGSPAPI('/Lienthonghotich/1.0/nhanHoSoDKHT', requestData, token);
```

### Handle SSL certificate acceptance:
```javascript
openNGSPCertificateAcceptance(); // Opens certificate acceptance page
```

## Troubleshooting:

### Lỗi SSL Certificate:
- Hệ thống sẽ tự động fallback từ HTTPS sang HTTP
- Check console logs để xem protocol nào được sử dụng
- Nếu cả hai đều fail, follow hướng dẫn SSL setup ở trên

### Network Errors:
- Check VPN connection
- Verify api.ngsp.gov.vn accessibility
- Check firewall settings

### Authentication Errors:
- Verify username/password trong config
- Check token expiration
- Verify API endpoint URLs

## Logs để debug:

Hệ thống sẽ log các thông tin sau:
- `🚀 NGSP API Request: POST https://api.ngsp.gov.vn/token`
- `✅ NGSP API Response: 200 OK`
- `❌ HTTPS attempt failed: SSL Error`
- `🔄 Trying NGSP API via HTTP: http://api.ngsp.gov.vn/token`

## Security Notes:

- HTTP fallback chỉ nên sử dụng trong development
- Production nên sử dụng proper SSL certificates
- Credentials được hardcode trong config - cần move to environment variables
- SSL bypass chỉ hoạt động limited trong browser environment
