import React from "react";

interface SelectOptionProps {
  value: string;
  onChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
}

const SelectOption: React.FC<SelectOptionProps> = ({ value, onChange }) => {
  return (
    <select value={value} onChange={onChange}>
      <option value="Tiếp nhận qua VBDLIS">Tiếp nhận qua VBDLIS</option>
      <option value="Cập nhật lại file kết quả">
        Cập nhật lại file kết quả
      </option>
      <option value="Cập nhật trạng thái hồ sơ VBDLIS">
        Cập nhật trạng thái hồ sơ VBDLIS
      </option>
      <option value="Đồng bộ bổ sung">Đồng bộ bổ sung qua một cửa</option>

      <option value="<PERSON>y<PERSON>n sang task đang xử lý"><PERSON><PERSON><PERSON><PERSON> sang task đang xử lý</option>
      <option value="<PERSON>y<PERSON><PERSON> sang bước kết quả khi đang ở bước đang xử lý"><PERSON><PERSON><PERSON><PERSON> sang bước kết quả khi đang ở bước đang xử lý</option>
      <option value="Chuyển sang bước chờ NVTC">Chuyển sang bước chờ NVTC</option>
      <option value="Chuyển sang bước TH NVTC">Chuyển sang bước TH NVTC</option>
      <option value="Chuyển sang bước kết quả khi đang đứng ở bước TH NVTC">Chuyển sang bước kết quả khi đang đứng ở bước TH NVTC</option>
    </select>
  );
};

export default SelectOption;
