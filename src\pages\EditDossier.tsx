import React, { useState, useEffect } from "react";
import axios from "axios";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { format } from "date-fns";
import MessageDisplay from "../components/MessageDisplay";
import TextArea from "../components/TextArea";
import Button from "../components/Button";
import { FaTrash, FaRegEdit, FaPlusCircle, FaAddressBook, FaBook } from "react-icons/fa";
import "./css/EditDossier.css";
import Snackbar from "../components/Snackbar"; // Import Snackbar
// import các biến cấu hình
import { API_BASE_URL } from '../config';


const EditDossier: React.FC = () => {
  const [inputText, setInputText] = useState<string>("");
  const [dossierDetails, setDossierDetails] = useState<any[]>([]);
  const [message, setMessage] = useState<string>("");
  const [isError, setIsError] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [snackbarVisible, setSnackbarVisible] = useState<boolean>(false); // Snackbar visibility
  const [selection, setSelection] = useState<string>("hoSo"); // State for radio button selection
  const [durations, setDurations] = useState<string[]>([]);

  const handleSelectionChange = (newSelection: any) => {
    setMessage("");
    setDossierDetails([]);
    setIsError(false);
    setSnackbarVisible(false);
    setSelection(newSelection);
  };

  useEffect(() => {
    // Initialize durations array with empty values
    setDurations(new Array(dossierDetails.length).fill("Đang tính toán..."));

    // Fetch durations for each detail
    dossierDetails.forEach(async (detail, index) => {
      const duration = await handleCalculateDuration(detail);
      setDurations((prev) => {
        const updated = [...prev];
        updated[index] = duration;
        return updated;
      });
    });
  }, [dossierDetails]);

  const convertTime = (timeString: any) => {
    // Chuyển đổi chuỗi thời gian thành đối tượng Date
    const date = new Date(timeString);

    // Chuyển đổi về định dạng ISO và lấy phần thời gian ở UTC
    const utcDate = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());

    // Định dạng lại về chuỗi
    return utcDate.toISOString(); // kết quả sẽ là '2024-10-21T00:00:00.000Z'
  };

  const fetchToken = async (): Promise<string> => {
    const accessToken = localStorage.getItem("access_token");
    if (!accessToken) {
      throw new Error("Token không tồn tại.");
    }
    return accessToken;
  };

  const fetchDossierDetails = async (dossierCode: string) => {
    try {
      const token = await fetchToken();
      let detailUrl = "";
      const dossierUrl = `${API_BASE_URL}/pa/dossier/${dossierCode}/--by-code`;
      const dossierResponse = await axios.get(dossierUrl, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!dossierResponse.data.id) {
        throw new Error(`Dossier information not found for ${dossierCode}`);
      }

      const id = dossierResponse.data.id;
      const acceptedDate = dossierResponse.data.acceptedDate;
      const appointmentDate = dossierResponse.data.appointmentDate;
      const completedDate = dossierResponse.data.completedDate;
      if (selection === "hoSo") {
        // console.log(`code: ${dossierCode}, id: ${id}`);
        console.log(`id: ${id}, code: ${dossierCode}, Ngày tiếp nhận: ${acceptedDate}, Ngày có kết quả: ${completedDate}, Ngày hẹn trả: ${appointmentDate}, Ngày hẹn trả UTC: ${convertTime(appointmentDate)}`);
        detailUrl = `${API_BASE_URL}/pa/dossier/${id}/--online`;
      } else {
        detailUrl = `${API_BASE_URL}/re/general-report-qni/--etl-dossier-by-id?dossier-id=${id}`;
      }

      const detailResponse = await axios.get(detailUrl, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (selection === "baoCao") {
        const deadlineAcceptedDate = detailResponse.data.deadlineAcceptedDate;
        const pauseDate = detailResponse.data.pauseDate;
        const extendDate = detailResponse.data.extendDate;
        console.log(`id: ${id}, code: ${dossierCode}, Ngày tiếp nhận: ${acceptedDate}, Ngày có kết quả: ${completedDate}, Ngày hẹn trả: ${appointmentDate}, Ngày hẹn trả UTC: ${convertTime(appointmentDate)}, Hạn tiếp nhận: ${deadlineAcceptedDate}, Ngày tạm dừng: ${pauseDate}, Ngày gia hạn: ${extendDate}`);
      }

      return { ...detailResponse.data, selectedFiles: [] }; // Initialize selectedFiles for each dossier
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        throw new Error(error.message);
      } else {
        throw new Error("An unknown error occurred.");
      }
    }
  };

  const handleFetchDetails = async () => {
    setLoading(true);
    setMessage("");
    setIsError(false);
    setDossierDetails([]);

    try {
      const dossierCodes = inputText.split(",").map((code) => code.trim());
      const details = [];

      for (const code of dossierCodes) {
        const detail = await fetchDossierDetails(code);
        if (detail) {
          details.push(detail);
        }
      }

      setDossierDetails(details);
      setMessage("Lấy chi tiết hồ sơ thành công.");
      setSnackbarVisible(true); // Show Snackbar
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        setMessage(`Có lỗi xảy ra: ${error.message}`);
      } else {
        setMessage(`Có lỗi xảy ra: ${String(error)}`); // Handle unknown errors
      }
      setIsError(true);
      setSnackbarVisible(true); // Show Snackbar
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (
    date: Date | null,
    field: string,
    index: number
  ) => {
    const updatedDetails = [...dossierDetails];
    if (date) {
      updatedDetails[index][field] = format(
        date,
        "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"
      );
    } else {
      updatedDetails[index][field] = null;
    }
    setDossierDetails(updatedDetails);
  };

  const handleChangeText = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(event.target.value);
    setMessage("");
    setDossierDetails([]);
    setIsError(false);
    setSnackbarVisible(false);
  };

  const handleUpdateAppointmentDates = async () => {
    setLoading(true);
    setMessage("");
    setIsError(false);

    const accessToken = await fetchToken();

    const updateData = dossierDetails.map((detail) => ({
      id: detail.id,
      appointmentDate: detail.appointmentDate,
      dueDate: detail.appointmentDate,
    }));

    try {
      const response = await axios.put(
        `${API_BASE_URL}/pa/dossier/--date`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.status === 200) {
        setMessage("Cập nhật ngày hẹn trả thành công.");
        setSnackbarVisible(true); // Show Snackbar
      } else {
        setMessage("Cập nhật không thành công.");
        setIsError(true);
        setSnackbarVisible(true); // Show Snackbar
      }
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        setMessage(`Có lỗi xảy ra: ${error.message}`);
      } else {
        setMessage(`Có lỗi xảy ra: ${String(error)}`); // Handle unknown errors
      }
      setIsError(true);
      setSnackbarVisible(true); // Show Snackbar
    }
    finally {
      setLoading(false);
    }
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    if (event.target.files) {
      const files = Array.from(event.target.files);
      const updatedDetails = [...dossierDetails];
      updatedDetails[index].selectedFiles = files; // Update selectedFiles for the specific dossier
      setDossierDetails(updatedDetails);
    }
  };

  const handleRemoveFile = (detailIndex: number, fileIndex: number) => {
    const updatedDetails = [...dossierDetails];
    updatedDetails[detailIndex].selectedFiles = updatedDetails[
      detailIndex
    ].selectedFiles.filter((_: File, i: number) => i !== fileIndex);
    setDossierDetails(updatedDetails);
  };

  const uploadFiles = async (selectedFiles: File[]): Promise<any[]> => {
    if (selectedFiles.length === 0) {
      throw new Error("Chưa chọn tệp tin nào.");
    }

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });

    const token = await fetchToken();

    const response = await axios.post(
      `${API_BASE_URL}/fi/file/--multiple?uuid=1`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      }
    );

    return response.data;
  };

  const updateAttachments = async (attachments: any[], dossierId: string) => {
    const token = await fetchToken();

    const data = {
      attachment: attachments.map((attachment) => ({
        id: attachment.id,
        filename: attachment.filename,
        size: attachment.size,
        group: "5f9bd9692994dc687e68b5a6",
      })),
    };

    const response = await axios.put(
      `${API_BASE_URL}/pa/dossier/${dossierId}/--online`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.status !== 200) {
      throw new Error("Cập nhật tệp đính kèm không thành công.");
    }
  };

  const handleNewUpload = async (dossierId: string, index: number) => {
    setLoading(true);
    setMessage("");
    setIsError(false);

    try {
      const uploadedFiles = await uploadFiles(
        dossierDetails[index].selectedFiles
      );
      await updateAttachments(uploadedFiles, dossierId);
      setMessage("Cập nhật mới tệp đính kèm thành công.");
      setSnackbarVisible(true); // Show Snackbar
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        setMessage(`Có lỗi xảy ra: ${error.message}`);
      } else {
        setMessage(`Có lỗi xảy ra: ${String(error)}`); // Handle unknown errors
      }
      setIsError(true);
      setSnackbarVisible(true); // Show Snackbar
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (date: any) => {
    return date ? format(new Date(date), 'dd/MM/yyyy HH:mm:ss') : '';
  };

  const handleAdditionalUpload = async (dossierId: string, index: number) => {
    setLoading(true);
    setMessage("");
    setIsError(false);

    try {
      const uploadedFiles = await uploadFiles(
        dossierDetails[index].selectedFiles
      );
      const existingAttachments = dossierDetails[index].attachment || [];

      const allAttachments = existingAttachments.concat(uploadedFiles);

      await updateAttachments(allAttachments, dossierId);
      setMessage("Cập nhật thêm tệp đính kèm thành công.");
      setSnackbarVisible(true); // Show Snackbar
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        setMessage(`Có lỗi xảy ra: ${error.message}`);
      } else {
        setMessage(`Có lỗi xảy ra: ${String(error)}`); // Handle unknown errors
      }
      setIsError(true);
      setSnackbarVisible(true); // Show Snackbar
    } finally {
      setLoading(false);
    }
  };

  const handleCalculateDuration = async (detail: any): Promise<string> => {
    try {
      if (detail.extendQNI?.isVbdlis && detail.task?.[3]) {
        const token = await fetchToken();
        const startDateUTC = new Date(detail.task[3].assignedDate).toISOString();
        const endDateUTC = new Date(detail.task[3].confirmFFODate).toISOString();

        const response = await fetch('https://apiigate.quangngai.gov.vn/bt/timesheet-gen/--gen-duration-by-dossier-id', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify([
            {
              timesheet: { id: '623849e3e63b54793b9ff57e' },
              dossier: { id: detail.id },
              startDate: startDateUTC,
              endDate: endDateUTC,
              checkOffDay: true,
              offTime: '15:00',
              processingTimeUnit: 'd',
            },
          ]),
        });

        const result = await response.json();
        return result[0]?.duration ? `${result[0].duration} Ngày` : "Không có dữ liệu";
      } else {
        return "Dữ liệu không hợp lệ";
      }
    } catch (error) {
      console.error("Error calculating duration:", error);
      return "Lỗi khi tính toán";
    }
  };


  return (
    <div className="form-cotainer">
      <TextArea value={inputText} onChange={handleChangeText} />
      <div className="radio-buttons processing-content-buttons">
        <label>
          <input
            type="radio"
            value="hoSo"
            checked={selection === "hoSo"}
            onChange={() => handleSelectionChange("hoSo")}
          />
          Hồ sơ
        </label>
        <label>
          <input
            type="radio"
            value="baoCao"
            checked={selection === "baoCao"}
            onChange={() => handleSelectionChange("baoCao")}
          />
          Báo cáo
        </label>
      </div>
      <div className="processing-content-buttons">
        <Button onClick={handleFetchDetails} disabled={loading || !inputText.trim()}>
          <FaBook />
          {loading ? "Đang lấy chi tiết..." : "Lấy chi tiết hồ sơ"}
        </Button>
      </div>

      <MessageDisplay message={message} isError={isError} />
      {snackbarVisible && (
        <Snackbar message={message} isError={isError} onClose={() => setSnackbarVisible(false)} />
      )}

      {dossierDetails.map((detail, index) => (
        <div key={index} className="dossier-detail">
          <div className="dossier-code-container">
            <div className="dossier-code"><a href={`https://motcua.quangngai.gov.vn/vi/dossier/search/${detail.id}`} target="_blank"> Mã hồ sơ: {detail.code} </a></div>
          </div>
          <div>
            <label>Trạng thái hồ sơ: </label>
            <span>{selection === "baoCao" ? detail.dossierStatus?.name : detail.dossierStatus.name[0]?.name}</span>
          </div>
          <div>
            <label>Trạng thái task hồ sơ: </label>
            <span>{detail.dossierTaskStatus?.name}</span>
          </div>
          <div>
            <label>Chủ hồ sơ: </label>
            <span>{selection === "baoCao" ? detail.applicant?.ownerFullName : detail.applicant?.data?.ownerFullname}</span>
          </div>
          <div>
            <label>Người nộp hồ sơ: </label>
            <span>{selection === "baoCao" ? detail.applicant?.fullname : detail.applicant?.data?.fullname}</span>
          </div>
          <div>
            <label>Trạng thái menu hồ sơ: </label>
            <span>{detail.dossierMenuTaskRemind?.name}</span>
          </div>
          {
            selection !== "baoCao" && (
              <>
                <div>
                  <label>Thủ tục: </label>
                  <span>{detail.procedure?.translate[0]?.name}</span>
                </div>
                <div>
                  <label>CMND/CCCD: </label>
                  <span>{detail.applicant?.data?.identityNumber}</span>
                </div>
              </>
            )
          }

          {selection === "baoCao" && (
            <>
              <div>
                <label>Đính file kết quả: </label>
                <span>{detail.extendDigitizing?.isHaveAttachment ? "Có" : "Không"}</span>
              </div>
              <div>
                <label>Đính file thành phần hồ sơ: </label>
                <span>{detail.extendDigitizing?.isHaveTPHS ? "Có" : "Không"}</span>
              </div>
              <div>
                <label>Ngày rút hồ sơ: </label>
                <span>{detail.withdrawDate ? formatDateTime(detail.withdrawDate) : ""}</span>
              </div>
              <div>
                <label>Ngày thực hiện nghĩa vụ tài chính: </label>
                <span>{detail.financialObligationsDate ? formatDateTime(detail.financialObligationsDate) : ""}</span>
              </div>
              <div>
                <label>Ngày dừng xử lý: </label>
                <span>{detail.cancelledDate ? formatDateTime(detail.cancelledDate) : ""}</span>
              </div>
              <div>
                <label>Thời hạn tiếp nhận: </label>
                <span>{detail.deadlineAcceptedDate ? formatDateTime(detail.deadlineAcceptedDate) : ""}</span>
              </div>
              <div>
                <label>Chậm tiếp nhận: </label>
                <span>{detail?.islowToReceive ? "Có" : "Không"}</span>
              </div>
            </>
          )}
          <div>
            <label>Ngày nộp hồ sơ: </label>
            <DatePicker
              selected={detail.appliedDate ? new Date(detail.appliedDate) : null}
              onChange={(date) => handleDateChange(date, "appliedDate", index)}
              dateFormat="dd/MM/yyyy HH:mm:ss"
            />
          </div>
          <div>
            <label>Ngày tiếp nhận: </label>
            <DatePicker
              selected={detail.acceptedDate ? new Date(detail.acceptedDate) : null}
              onChange={(date) => handleDateChange(date, "acceptedDate", index)}
              dateFormat="dd/MM/yyyy HH:mm:ss"
            />
          </div>
          <div>
            <label>Ngày hẹn trả: </label>
            <DatePicker
              selected={detail.appointmentDate ? new Date(detail.appointmentDate) : null}
              onChange={(date) => handleDateChange(date, "appointmentDate", index)}
              dateFormat="dd/MM/yyyy HH:mm:ss"
              showTimeSelect
            />
          </div>
          <div>
            <label>Ngày kết thúc: </label>
            <DatePicker
              selected={detail.completedDate ? new Date(detail.completedDate) : null}
              onChange={(date) => handleDateChange(date, "completedDate", index)}
              dateFormat="dd/MM/yyyy HH:mm:ss"
            />
          </div>
          <div >
            <label>Ngày THNVTC: </label>
            <span>{durations[index]}</span>
          </div>
          <div className="file-attachment-section">
            <div>
              <label>Tệp đính kèm: </label>
              <ul>
                {detail.attachment && detail.attachment.length > 0 ? (
                  detail.attachment.map((file: any, fileIndex: number) => (
                    <li key={fileIndex}>
                      <a
                        href={`${API_BASE_URL}/fi/wopi/files/${file.id}/contents`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {file.filename}
                      </a>
                    </li>
                  ))
                ) : (
                  <li>Không có tệp đính kèm</li>
                )}
              </ul>
            </div>
            <div>
              <label>Chọn tệp đính kèm mới: </label>
              <input type="file" multiple onChange={(e) => handleFileChange(e, index)} />
              <ul>
                {detail.selectedFiles.map((file: File, fileIndex: number) => (
                  <li key={fileIndex} className="file-item">
                    {file.name}
                    <button
                      type="button"
                      className="remove-button"
                      onClick={() => handleRemoveFile(index, fileIndex)}
                    >
                      <FaTrash />
                    </button>
                  </li>
                ))}
              </ul>
            </div>
            <div className="detail-content-buttons">
              <Button onClick={() => handleNewUpload(detail.id, index)} disabled={loading} className="new-upload">
                <FaRegEdit />
                Cập nhật mới
              </Button>
              <Button onClick={() => handleAdditionalUpload(detail.id, index)} disabled={loading} className="additional-upload">
                <FaPlusCircle />
                Cập nhật thêm
              </Button>
            </div>
          </div>

        </div>
      ))}

      {dossierDetails.length > 0 && (
        <Button onClick={handleUpdateAppointmentDates} disabled={loading}>
          {loading ? "Đang cập nhật..." : "Cập nhật ngày hẹn trả"}
        </Button>
      )}
    </div>
  )
};

export default EditDossier;
