import React from "react";
import "./MessageDisplay.css"; // Add necessary styles here

interface MessageDisplayProps {
  message?: string; // Make message prop optional
  isError: boolean;
}

const MessageDisplay: React.FC<MessageDisplayProps> = ({
  message = "", // Default value or handle the case where message is undefined
  isError,
}) => {
  return (
    <div
      className={`message-display ${message ? "show" : "hide"} ${
        isError ? "error" : "success"
      }`}
    >
      {message && <pre>{message}</pre>}
    </div>
  );
};

export default MessageDisplay;
