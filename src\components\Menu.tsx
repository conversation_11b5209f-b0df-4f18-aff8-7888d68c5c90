import React, { useState } from "react";
import {
  FaSyncAlt,
  FaFolderOpen,
  FaCaretDown,
  FaCaretRight,
  FaFile,
  FaGlobeAmericas,
  FaCheck,
  FaBook,
  FaFileSignature,
  FaEye,
} from "react-icons/fa"; // Import các biểu tượng từ React Icons
import "./Menu.css";

interface MenuProps {
  setView: (view: string) => void; // Or React.Dispatch<React.SetStateAction<string>>
}

const Menu: React.FC<MenuProps> = ({ setView }) => {
  const [showSyncSubMenu, setShowSyncSubMenu] = useState<boolean>(false);
  const [showRepairSubMenu, setShowRepairSubMenu] = useState<boolean>(false);

  const toggleSyncSubMenu = () => {
    setShowSyncSubMenu(!showSyncSubMenu);
    setShowRepairSubMenu(false); // Đóng submenu Khắc phụ<PERSON> hồ sơ nếu mở
  };

  const toggleRepairSubMenu = () => {
    setShowRepairSubMenu(!showRepairSubMenu);
    setShowSyncSubMenu(false); // Đóng submenu Khắc phục đồng bộ nếu mở
  };

  const handleSyncSubMenuClick = (view: string) => {
    console.log(`Navigate to ${view}`);
    setView(view); // Thay đổi view để điều hướng tới trang tương ứng
  };

  const handleRepairSubMenuClick = (view: string) => {
    console.log(`Navigate to ${view}`);
    setView(view); // Thay đổi view để điều hướng tới trang tương ứng
  };

  return (
    <div className="menu">
      <div className="menu-header">
        <button
          onClick={toggleSyncSubMenu}
          className={`menu-button ${showSyncSubMenu ? "active" : ""}`}
        >
          <FaSyncAlt className="menu-icon" />
          Khắc phục đồng bộ
          <span className={`dropdown-icon ${showSyncSubMenu ? "rotate" : ""}`}>
            {showSyncSubMenu ? <FaCaretDown /> : <FaCaretRight />}
          </span>
        </button>
        {showSyncSubMenu && (
          <div className="submenu">
            <button
              className="submenu-button"
              onClick={() => handleSyncSubMenuClick("formVBDLIS")}
            >
              <FaGlobeAmericas className="submenu-icon" />
              VBDLIS
            </button>
            <button
              className="submenu-button"
              onClick={() => handleSyncSubMenuClick("formBTXH")}
            >
              <FaFolderOpen className="submenu-icon" />
              BTXH
            </button>
            <button
              className="submenu-button"
              onClick={() => handleSyncSubMenuClick("formDVCLT")}
            >
              <FaFile className="submenu-icon" />
              DVCLT
            </button>
            <button
              className="submenu-button"
              onClick={() => handleSyncSubMenuClick("logDisplayDemo")}
            >
              <FaEye className="submenu-icon" />
              Demo: Hiển thị log DVCLT
            </button>
            {/* Thêm các submenu item cho Khắc phục đồng bộ tại đây */}
          </div>
        )}
      </div>
      <div className="menu-header">
        <button
          onClick={toggleRepairSubMenu}
          className={`menu-button ${showRepairSubMenu ? "active" : ""}`}
        >
          <FaFile className="menu-icon" />
          Khắc phục hồ sơ
          <span
            className={`dropdown-icon ${showRepairSubMenu ? "rotate" : ""}`}
          >
            {showRepairSubMenu ? <FaCaretDown /> : <FaCaretRight />}
          </span>
        </button>
        {showRepairSubMenu && (
          <div className="submenu">
            <button
              className="submenu-button"
              onClick={() => handleRepairSubMenuClick("formRepairDossier")}
            >
              <FaCheck className="submenu-icon" />
              Cập nhật trạng thái
            </button>
            <button
              className="submenu-button"
              onClick={() =>
                handleRepairSubMenuClick("updateProcessingContent")
              }
            >
              <FaBook className="submenu-icon" />
              Cập nhật nội dung xử lý
            </button>
            <button
              className="submenu-button"
              onClick={() => handleRepairSubMenuClick("editDossier")}
            >
              <FaFileSignature className="submenu-icon" />
              Chỉnh sửa hồ sơ
            </button>
            {/* Thêm các submenu item cho Khắc phục hồ sơ tại đây */}
          </div>
        )}
      </div>
    </div>
  );
};

export default Menu;
