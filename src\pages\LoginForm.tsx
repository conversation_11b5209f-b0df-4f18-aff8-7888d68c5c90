import React, { useEffect, useState } from "react";
import axios from "axios";
import "./css/LoginForm.css";
import backgroundImage from "../images/background-image.jpg"; // Import background image
import { SSO_BASE_URL } from '../config';

interface LoginFormProps {
  onLoginSuccess: (username: string, tokenData: any) => void; // Callback function to handle successful login
}

const LoginForm: React.FC<LoginFormProps> = ({ onLoginSuccess }) => {
  const [username, setUsername] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [rememberMe, setRememberMe] = useState<boolean>(false); // State for "Remember Me" checkbox
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const rememberMeValue = localStorage.getItem("remember_me");
    if (rememberMeValue) {
      setRememberMe(rememberMeValue === "true");

      // Restore username and password if rememberMe is true
      if (rememberMeValue === "true") {
        const storedUsername = localStorage.getItem("username");
        const storedPassword = localStorage.getItem("password");
        if (storedUsername) setUsername(storedUsername);
        if (storedPassword) setPassword(storedPassword);
      }
    }
  }, []);

  const handleLogin = async () => {
    try {
      const formData = new URLSearchParams();
      formData.append("grant_type", "password");
      formData.append("username", username);
      formData.append("password", password);
      formData.append("client_id", "web-onegate");
  
      const response = await axios.post(
        `${SSO_BASE_URL}/auth/realms/digo/protocol/openid-connect/token`,
        formData.toString(),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
  
      if (response.status === 200) {
        const { access_token, expires_in } = response.data;
        const tokenData = { access_token, expires_in };
  
        // Store token and expiration in localStorage
        localStorage.setItem("access_token", access_token);
        localStorage.setItem("expires_in", expires_in.toString());
        localStorage.setItem("username-showUI", username);
  
        if (rememberMe) {
          localStorage.setItem("remember_me", "true");
          localStorage.setItem("username", username);
          localStorage.setItem("password", password);
        } else {
          localStorage.removeItem("remember_me");
          localStorage.removeItem("username");
          localStorage.removeItem("password");
        }
  
        setError("");
        onLoginSuccess(username, tokenData);
      } else {
        setError("Failed to login. Please check your credentials.");
      }
    } catch (error) {
      setError("Failed to login. Please try again later.");
    }
  };
  

  const handleCheckboxChange = () => {
    setRememberMe(!rememberMe);
  };

  return (
    <div
      className="login-form-container"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="login-form">
        <h2>ĐĂNG NHẬP</h2>
        <form onSubmit={(e) => e.preventDefault()}>
          <div className="form-group">
            <label>Username:</label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
          </div>
          <div className="form-group">
            <label>Password:</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <div className="remember-me">
            <input
              type="checkbox"
              id="rememberMe"
              checked={rememberMe}
              onChange={handleCheckboxChange}
            />
            <label htmlFor="rememberMe">Ghi nhớ tài khoản</label>
          </div>
          <button type="submit" onClick={handleLogin}>
            Đăng nhập
          </button>
          {error && <p className="error-message">{error}</p>}
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
