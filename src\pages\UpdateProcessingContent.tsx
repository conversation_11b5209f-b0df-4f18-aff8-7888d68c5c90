import React, { useState, useEffect } from "react";
import TextArea from "../components/TextArea";
import Button from "../components/Button";
import MessageDisplay from "../components/MessageDisplay";
import axios from "axios";
import DatePicker from "react-datepicker";
import { format } from "date-fns";
import "./css/UpdateProcessingContent.css";
import Snackbar from "../components/Snackbar"; // Import Snackbar
import { API_BASE_URL, SSO_BASE_URL } from '../config';
import { FaTrash, FaCheckCircle, FaBook } from 'react-icons/fa';
import TextAreaContent from "../components/TextAreaContent";

interface ProcessingContent {
  id: string;
  groupId: number;
  itemId: string;
  user: {
    id: string;
    fullname: string;
  };
  content: string;
  file: any[];
  createdDate: string;
  updatedDate: string;
  taskId: string | null;
  dossierCode: string;
  selectedFiles?: File[];
}

const UpdateProcessingContent: React.FC = () => {
  const [inputText, setInputText] = useState<string>("");
  const [processingContents, setProcessingContents] = useState<ProcessingContent[]>([]);
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [updatedDate, setUpdatedDate] = useState<Date | null>(null);
  const [snackbarVisible, setSnackbarVisible] = useState<boolean>(false);

  // useEffect(() => {
  //   if (processingContents.length > 0) {
  //     setMessage("Dữ liệu đã có tác động, vui lòng lưu ý trước khi cập.");
  //   }
  // }, [processingContents]);

  const handleChangeText = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIsError(false);
    setInputText(event.target.value);
    setSnackbarVisible(false);
    setMessage("");
    setProcessingContents([]);
  };

  const handleContentChange = (event: React.ChangeEvent<HTMLTextAreaElement>, index: number) => {
    const updatedProcessingContents = [...processingContents];
    updatedProcessingContents[index].content = event.target.value;
    setProcessingContents(updatedProcessingContents);
  };

  const fetchToken = async (): Promise<string | null> => {
    const accessToken = localStorage.getItem("access_token");
    if (!accessToken) {
      console.error("Không tìm thấy accessToken trong localStorage");
      return null;
    }

    return accessToken;
  };

  const handleDateTimeChange = (date: Date | null, index: number) => {
    const updatedProcessingContents = [...processingContents];
    const item = updatedProcessingContents[index];
    const originalCreatedDate = item.createdDate;

    if (date && originalCreatedDate !== format(date, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx")) {
      item.createdDate = format(date, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");
      item.updatedDate = format(date, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");
    }

    setProcessingContents(updatedProcessingContents);
  };



  const handleFetchContent = async () => {
    setLoading(true);
    try {
      const codes = inputText.split(",").map((record) => record.trim());
      const accessToken = await fetchToken();
      const allProcessingContents: ProcessingContent[] = [];

      for (const code of codes) {
        const dossierUrl = `${API_BASE_URL}/pa/dossier/${code}/--by-code`;
        const dossierResponse = await axios.get(dossierUrl, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });

        if (!dossierResponse.data.id) {
          console.error(`Dossier information not found for ${code}`);
          setMessage(`Dossier information not found for ${code}`);
          setSnackbarVisible(true);
          setIsError(true);
          continue;
        }

        const dossierId = dossierResponse.data.id;
        const contentUrl = `${API_BASE_URL}/me/comment?group-id=2&item-id=${dossierId}&size=50`;
        const contentResponse = await axios.get(contentUrl, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });

        if (!contentResponse.data.content || contentResponse.data.content.length === 0) {
          console.error(`No processing content found for ${code}`);
          setMessage(`Không có nội dung xử lý của hồ sơ: ${code}`);
          setSnackbarVisible(true);
          setIsError(true);
          continue;
        }

        const processedContents: ProcessingContent[] = contentResponse.data.content
          .map((item: any) => ({
            id: item.id,
            groupId: item.groupId,
            itemId: item.itemId,
            user: {
              id: item.user.id,
              fullname: item.user.fullname,
            },
            content: item.content,
            file: item.file || [],
            createdDate: item.createdDate
              ? format(new Date(item.createdDate), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx")
              : null,
            updatedDate: item.updatedDate
              ? format(new Date(item.updatedDate), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx")
              : null,
            taskId: item.taskId,
            dossierCode: code,
            selectedFiles: [] // Initialize selectedFiles as an empty array
          }))
          .sort((a: ProcessingContent, b: ProcessingContent) => {
            if (a.createdDate && b.createdDate) {
              return new Date(a.createdDate).getTime() - new Date(b.createdDate).getTime();
            }
            return 0;
          });

        allProcessingContents.push(...processedContents);
      }

      setProcessingContents(allProcessingContents);
      allProcessingContents.length > 0
        ? setMessage("Lấy nội dung xử lý thành công.")
        : setMessage("Không có dữ liệu trả về.");
      setIsError(false);
      setSnackbarVisible(true);
    } catch (error: any) {
      setMessage(`Có lỗi xảy ra: ${error.message}`);
      setSnackbarVisible(true);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const files = Array.from(e.target.files || []);
    const updatedProcessingContents = [...processingContents];
    updatedProcessingContents[index].selectedFiles = files;
    setProcessingContents(updatedProcessingContents);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setIsError(false);
    setMessage("");

    const accessToken = await fetchToken();

    try {
      const response = await axios.put(
        `${API_BASE_URL}/me/comment/--update-date-qni`,
        { content: processingContents },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.status === 200) {
        setMessage("Cập nhật nội dung xử lý thành công");
        setSnackbarVisible(true); // Set state to show snackbar
      } else {
        setMessage("Cập nhật không thành công");
        setIsError(true);
      }
    } catch (error: any) {
      setMessage(`Có lỗi xảy ra: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFile = (index: number, fileIndex: number) => {
    const updatedProcessingContents = [...processingContents];
    updatedProcessingContents[index].selectedFiles?.splice(fileIndex, 1);
    setProcessingContents(updatedProcessingContents);
  };

  const handleDeleteContent = async (contentId: string) => {
    const accessToken = await fetchToken();
    try {
      await axios.delete(`${API_BASE_URL}/me/comment/${contentId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      setMessage("Xóa nội dung xử lý thành công");
      setSnackbarVisible(true);
      // Remove deleted content from the list
      setProcessingContents((prevContents) =>
        prevContents.filter((content) => content.id !== contentId)
      );
    } catch (error: any) {
      setMessage(`Có lỗi xảy ra: ${error.message}`);
      setSnackbarVisible(true);
      setIsError(true);
    }
  };

  const handleUpdateContent = async (index: number) => {
    const accessToken = await fetchToken();
    const item = processingContents[index];

    try {
      // Upload new files if any
      if (item.selectedFiles && item.selectedFiles.length > 0) {
        const formData = new FormData();
        item.selectedFiles.forEach((file) => {
          formData.append("files", file);
        });

        const uploadResponse = await axios.post(
          `${API_BASE_URL}/fi/file/--multiple?uuid=1`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "multipart/form-data",
            },
          }
        );

        item.file = uploadResponse.data;
      }

      // Update the comment
      const response = await axios.post(`${API_BASE_URL}/me/comment`, item, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      if (response.status === 200) {
        setMessage("Cập nhật nội dung xử lý thành công");
        if (response.data.id) {
          console.log(`delete: ${response.data.id}`)
          // Delete the existing comment
          await handleDeleteContent(item.id);
        }
        await handleFetchContent();
      } else {
        setMessage("Cập nhật không thành công");
        setIsError(true);
      }
      setSnackbarVisible(true);
    } catch (error: any) {
      setMessage(`Có lỗi xảy ra: ${error.message}`);
      setSnackbarVisible(true);
      setIsError(true);
    }
  };

  return (
    <div className="form-cotainer">
      <TextArea value={inputText} onChange={handleChangeText} />
      <div className="processing-content-buttons">
        <Button onClick={handleFetchContent} disabled={loading || !inputText.trim()}>
          <FaBook />
          {loading ? "Đang chạy..." : "Lấy nội dung xử lý"}
        </Button>
      </div>
      <MessageDisplay message={message} isError={isError} />
      {snackbarVisible && (
        <Snackbar
          message={message}
          isError={isError}
          onClose={() => setSnackbarVisible(false)}
        />
      )}
      {processingContents.length > 0 && (
        <div className="processing-contents">
          {(() => {
            const displayedCodes = new Set();
            return processingContents.map((item, index) => {
              const isFirstOccurrence = !displayedCodes.has(item.dossierCode);
              displayedCodes.add(item.dossierCode);

              return (
                <div key={index} className="processing-content">
                  {isFirstOccurrence && (
                    <div className="dossier-code-container">
                      <div className="dossier-code">
                        Mã hồ sơ: {item.dossierCode}
                      </div>
                    </div>
                  )}
                  <div>
                    <label>Nội dung xử lý: </label>
                    <TextAreaContent value={item.content} onChange={(e) => handleContentChange(e, index)} />
                  </div>
                  <div>
                    <label>Ngày xử lý:</label>
                    <DatePicker
                      selected={item.createdDate ? new Date(item.createdDate) : null}
                      onChange={(date) => handleDateTimeChange(date, index)}
                      showTimeSelect
                      dateFormat="dd/MM/yyyy HH:mm:ss"
                    />
                  </div>
                  <div>
                    <label>Người xử lý:</label>
                    <span>{item.user.fullname}</span>
                  </div>
                  <div className="file-attachment-section">
                    <div>
                      <label>File kết quả: </label>
                      <ul>
                        {item.file && item.file.length > 0 ? (
                          item.file.map((file: any, fileIndex: number) => (
                            <li key={fileIndex}>
                              <a
                                href={`${API_BASE_URL}/fi/wopi/files/${file.id}/contents`}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {file.filename}
                              </a>
                            </li>
                          ))
                        ) : (
                          <li>Không có tệp đính kèm</li>
                        )}
                      </ul>
                    </div>
                    <div>
                      <label>Chọn tệp đính kèm mới: </label>
                      <input
                        type="file"
                        multiple
                        onChange={(e) => handleFileChange(e, index)}
                      />
                      <ul>
                        {item.selectedFiles?.map((file: File, fileIndex: number) => (
                          <li key={fileIndex} className="file-item">
                            {file.name}
                            <button
                              type="button"
                              className="remove-button"
                              onClick={() => handleRemoveFile(index, fileIndex)}
                            >
                              <FaTrash />
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className="processing-content-buttons">
                    <Button
                      className="btn-update"
                      onClick={() => handleUpdateContent(index)}
                      disabled={loading}
                    >
                      <FaCheckCircle /> {loading ? "Đang chạy..." : "Cập nhật"}
                    </Button>
                    <Button
                      className="btn-delete"
                      onClick={() =>
                        window.confirm(`Bạn có chắc chắn muốn xóa nội dung xử lý: ${item.content}?`) &&
                        handleDeleteContent(item.id)
                      }
                      disabled={loading}
                    >
                      <FaTrash /> {loading ? "Đang chạy..." : "Xóa"}
                    </Button>
                  </div>

                </div>
              );
            });
          })()}
        </div>
      )}
      {processingContents.length > 0 && (
        <Button onClick={handleSubmit} disabled={loading}>
          {loading ? "Đang chạy..." : "Cập nhật ngày xử lý"}
        </Button>
      )}
    </div>
  );
};

export default UpdateProcessingContent;
