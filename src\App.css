body {
  font-family: Arial, sans-serif;
  background-color: #f7f7f7;
  margin: 0;
  padding: 0;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.main-content {
  display: flex;
  flex-grow: 1;
}

h1 {
  color: #333;
  font-size: 24px;
  margin-bottom: 20px;
  text-align: center;
  color: #fff;
}

textarea {
  width: 100%; /* Thay đổi từ 97% thành 100% */
  padding: 10px;
  font-size: 16px;
  border-radius: 4px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
  box-sizing: border-box; /* <PERSON><PERSON><PERSON> bảo padding và border không làm thay đổi kích thước */
}

select {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  border-radius: 4px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
  box-sizing: border-box; /* <PERSON><PERSON><PERSON> b<PERSON>o padding và border không làm thay đ<PERSON>i kích thước */
}

button {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #0056b3;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

button:hover:enabled {
  background-color: #0056b3;
}

pre {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin-top: 20px;
}

.app-container {
  display: flex;
  height: 100vh;
}

.content {
  flex-grow: 1;
  padding: 20px;
  width: 80%;
}

.blank-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 24px;
  color: #666;
}

.footer {
  background-color: #f4f4f4;
  color: #333;
  text-align: center;
  padding: 10px 20px;
}

.header {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-logo {
  width: 76px;
  height: 76px;
  margin-right: 10px;
}

.select-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.select-container label {
  margin-bottom: 8px;
  font-weight: bold;
}

.select-container select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  font-size: 16px;
}

.button-container {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* CSS cho button */
.button-container button {
  flex: 1;
}

.form-cotainer {
  width: 100%;
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  box-sizing: border-box;
}

.date-picker-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.date-picker-container label {
  margin-bottom: 8px;
  font-weight: bold;
}

/* Customize the date picker */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  font-size: 16px;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.user-info {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  z-index: 1000;
  display: flex; /* Hiển thị các phần tử con theo chiều ngang */
  align-items: center; /* Căn giữa theo chiều dọc */
  background-color: coral;
}

.user-info span {
  margin-right: 10px; /* Khoảng cách giữa tên người dùng và biểu tượng logout */
}

.user-info button {
  background: none; /* Bỏ màu nền của button */
  border: none; /* Bỏ viền của button */
  cursor: pointer;
}

.processing-content-buttons {
  display: flex;
  justify-content: center; /* Căn giữa các nút */
  gap: 10px; /* Khoảng cách giữa các nút */
  margin-top: 10px;
  width: 100%; /* Đảm bảo container chiếm toàn bộ chiều rộng */
}

.processing-content-buttons button {
  padding: 5px 10px; /* Giảm padding để nút nhỏ hơn */
  font-size: 14px; /* Giảm kích thước font */
  min-width: 75px; /* Đặt kích thước cố định nếu cần */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 150px; /* Thiết lập chiều rộng cho nút */
  height: 41px;
}