import React from "react";

interface TextAreaProps {
  value: string;
  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string; // Make placeholder optional
}

const TextArea: React.FC<TextAreaProps> = ({ value, onChange, placeholder }) => {
  // Xử lý thay đổi giá trị trong textarea
  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    let inputValue = event.target.value;

    // Tách chuỗi thành các mã hồ sơ
    let lines = inputValue.split(/\r?\n/);

    // Loại bỏ các dòng chỉ chứa khoảng trắng
    lines = lines.filter((line) => line.trim() !== "");

    // Xử lý từng dòng
    lines = lines.map((line, index) => {
      // Loại bỏ khoảng trắng không cần thiết
      let trimmedLine = line.trim();

      // Loại bỏ dấu ph<PERSON>y thừa
      trimmedLine = trimmedLine.replace(/,{2,}/g, ",");

      // Thêm dấu phẩy vào cuối nếu cần thiết
      if (
        trimmedLine &&
        !trimmedLine.endsWith(",") &&
        index < lines.length - 1
      ) {
        trimmedLine += ",";
      }

      return trimmedLine;
    });

    // Nối lại các dòng thành một chuỗi mới
    const updatedValue = lines.join("\n");

    // Gọi hàm onChange với giá trị đã được xử lý
    onChange({
      ...event,
      target: {
        ...event.target,
        value: updatedValue,
      },
    });
  };

  return (
    <textarea
      value={value}
      onChange={handleInputChange}
      rows={10}
      placeholder="Nhập danh sách mã hồ sơ ..." // Use placeholder prop here
    />
  );
};

export default TextArea;
