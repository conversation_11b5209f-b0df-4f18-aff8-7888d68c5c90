.snackbar {
  visibility: visible;
  min-width: 250px;
  margin-left: -125px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  border: 2px solid #fff;
  padding: 16px;
  position: fixed;
  z-index: 1;
  left: 50%;
  bottom: 30px;
  font-size: 17px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: opacity 0.3s ease-in-out;
  opacity: 1;
}

.snackbar.success {
  background-color: #4caf50;
  border-color: #2e7d32;
}

.snackbar.error {
  background-color: #e2847e;
  border-color: #d32f2f;
}

.snackbar-message {
  flex: 1; /* Takes up available space */
  text-align: left; /* Adjust text alignment as needed */
}

.snackbar-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 20px;
  margin-left: 10px;
  align-self: center; /* Aligns close button vertically centered */
  width: 45px;
}
