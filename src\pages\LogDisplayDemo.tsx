import React from "react";
import LogDisplayExample from "../components/LogDisplayExample";
import "./css/FormDVCLT.css";

const LogDisplayDemo: React.FC = () => {
  return (
    <div className="form-container">
      <div className="form-header">
        <h1>🔍 Demo: Cải tiến hiển thị log DVCLT</h1>
        <p>Xem trước cách hiển thị log mới với thông tin code rõ ràng</p>
      </div>
      
      <div style={{ 
        background: '#fef3c7', 
        padding: '1rem', 
        borderRadius: '8px', 
        border: '1px solid #f59e0b',
        marginBottom: '2rem'
      }}>
        <h3 style={{ margin: '0 0 0.5rem 0', color: '#92400e' }}>
          ⚡ Vấn đề trước đây:
        </h3>
        <p style={{ margin: 0, color: '#92400e' }}>
          <PERSON><PERSON> lấy log cho nhiều code (ví dụ: ABC123, XYZ789, DEF456), 
          các log chỉ hiển thị "Log 1", "Log 2", "Log 3" mà không biết log nào thuộc code nào.
        </p>
      </div>

      <div style={{ 
        background: '#d1fae5', 
        padding: '1rem', 
        borderRadius: '8px', 
        border: '1px solid #10b981',
        marginBottom: '2rem'
      }}>
        <h3 style={{ margin: '0 0 0.5rem 0', color: '#065f46' }}>
          ✅ Giải pháp mới:
        </h3>
        <p style={{ margin: 0, color: '#065f46' }}>
          Bây giờ mỗi log sẽ hiển thị rõ ràng: "Log 1 - Code: ABC123", "Log 2 - Code: XYZ789", v.v.
          Thông báo khi gửi lại cũng sẽ bao gồm thông tin code tương ứng.
        </p>
      </div>

      <LogDisplayExample />
      
      <div style={{
        background: '#eff6ff',
        padding: '1.5rem',
        borderRadius: '8px',
        border: '1px solid #3b82f6',
        marginTop: '2rem'
      }}>
        <h3 style={{ margin: '0 0 1rem 0', color: '#1e40af' }}>
          🚀 Hướng dẫn sử dụng:
        </h3>
        <ol style={{ margin: 0, paddingLeft: '1.5rem', color: '#1e40af' }}>
          <li>Vào trang "Cập nhật trạng thái sang DVCLT"</li>
          <li>Nhập nhiều code cách nhau bằng dấu phẩy (ví dụ: ABC123,XYZ789,DEF456)</li>
          <li>Nhấn "Lấy log" để xem kết quả</li>
          <li>Quan sát tiêu đề mỗi log sẽ hiển thị code tương ứng</li>
          <li>Khi gửi lại, thông báo cũng sẽ hiển thị code để dễ theo dõi</li>
        </ol>
      </div>
    </div>
  );
};

export default LogDisplayDemo;
