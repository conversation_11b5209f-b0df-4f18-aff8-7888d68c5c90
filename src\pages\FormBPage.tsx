import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import axios from "axios";
import "./css/FormBPage.css";
import Snackbar from "../components/Snackbar"; // Import Snackbar
import MessageDisplay from "../components/MessageDisplay";
import { FaSync } from "react-icons/fa";

const FormBPage: React.FC = () => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedOption, setSelectedOption] = useState<string>("GetDossier");
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [snackbarVisible, setSnackbarVisible] = useState<boolean>(false);

  const handleDateChange = (date: Date | null, type: "start" | "end") => {
    if (type === "start") {
      setStartDate(date);
    } else {
      setEndDate(date);
    }
  };

  const formatDateTime = (date: Date | null, endOfDay: boolean) => {
    if (!date) return "";
    const day = ("0" + date.getDate()).slice(-2);
    const month = ("0" + (date.getMonth() + 1)).slice(-2);
    const year = date.getFullYear();
    let hours = ("0" + date.getHours()).slice(-2);
    let minutes = ("0" + date.getMinutes()).slice(-2);
    let seconds = ("0" + date.getSeconds()).slice(-2);

    if (endOfDay) {
      hours = "23";
      minutes = "59";
      seconds = "59";
    }

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const handleOptionChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedOption(event.target.value);
  };

  const handleSync = async () => {
    if (!startDate || !endDate) return;

    const formattedStartDate = formatDateTime(startDate, false);
    const formattedEndDate = formatDateTime(endDate, true);
    const token = localStorage.getItem("access_token");
    setLoading(true);
    setMessage("");
    setIsError(false);
    setSnackbarVisible(false);

    let apiUrl = "";

    if (selectedOption === "GetDossier") {
      apiUrl = `http://10.52.50.19:8080/btxh-ktm/get-dossier-bldtbxh?from-date=${formattedStartDate}&to-date=${formattedEndDate}&update-tphs=false`;

      try {
        const response = await fetch(apiUrl, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const dataResponse = await response.json();
        const messageString = JSON.stringify(dataResponse, null, 2); // Chuyển đổi đối tượng thành chuỗi
        setMessage(`Đồng bộ thành công: \n ${messageString}`);
        setSnackbarVisible(true);
      } catch (error: any) {
        setIsError(true);
        setSnackbarVisible(true);
        setMessage(`Có lỗi xảy ra: ${error.message}`);
      }
      finally {
        setLoading(false);
      }

    } else if (selectedOption === "LastResult") {
      apiUrl = `http://10.52.50.19:8080/btxh-ktm/--sync-status-dossier?from-date=${formattedStartDate}&to-date=${formattedEndDate}&ignore-exist=false&ignore-appointmentDate=true`;

      try {
        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const dataResponse = await response.json();
        const messageString = JSON.stringify(dataResponse, null, 2); // Chuyển đổi đối tượng thành chuỗi
        setMessage(`Đồng bộ thành công: \n ${messageString}`);
        setSnackbarVisible(true);
      } catch (error: any) {
        setIsError(true);
        setSnackbarVisible(true);
        setMessage(`Có lỗi xảy ra: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };
  }

  return (
    <div className="form-cotainer">
      <div className="select-container">
        <label>Chọn loại đồng bộ:</label>
        <select value={selectedOption} onChange={handleOptionChange}>
          <option value="GetDossier">Đồng bộ danh sách hồ sơ</option>
          <option value="LastResult">Đồng bộ kết quả cuối cùng hồ sơ (Có kiểm tra sự tồn tại)</option>
        </select>
      </div>
      <div className="date-picker-container">
        <label>Từ ngày:</label>
        <DatePicker
          selected={startDate}
          onChange={(date) => handleDateChange(date as Date, "start")}
          selectsStart
          startDate={startDate as Date}
          endDate={endDate as Date}
          locale="vi"
          className="date-picker"
          dateFormat="dd/MM/yyyy"
        />
      </div>
      <div className="date-picker-container">
        <label>Đến ngày:</label>
        <DatePicker
          selected={endDate}
          onChange={(date) => handleDateChange(date as Date, "end")}
          selectsEnd
          startDate={startDate as Date}
          endDate={endDate as Date}
          minDate={startDate as Date}
          locale="vi"
          className="date-picker"
          dateFormat="dd/MM/yyyy"
        />
      </div>
      <div className="processing-content-buttons">
        <button onClick={handleSync} disabled={loading || !startDate || !endDate}>
          <FaSync />
          {loading ? "Đang chạy..." : "Đồng Bộ"}
        </button>
      </div>

      {snackbarVisible && (
        <Snackbar
          message={message}
          isError={isError}
          onClose={() => setSnackbarVisible(false)}
        />
      )}
      <MessageDisplay message={message} isError={isError} />
    </div>
  );
};

export default FormBPage;
