// testNGSP.js - Test script for NGSP API configuration
import { fetchNGSPToken, callNGSPAPI, openNGSPCertificateAcceptance } from './httpClient.js';
import { TOKEN_CONFIG } from '../config.js';

/**
 * Test NGSP token fetching
 */
export const testNGSPToken = async () => {
  console.log('🧪 Testing NGSP Token Fetch...');
  
  try {
    const token = await fetchNGSPToken(
      TOKEN_CONFIG.NGSP.username, 
      TOKEN_CONFIG.NGSP.password
    );
    
    if (token) {
      console.log('✅ NGSP Token fetch successful');
      console.log('Token length:', token.length);
      console.log('Token preview:', token.substring(0, 20) + '...');
      return token;
    } else {
      console.log('❌ NGSP Token fetch failed - no token returned');
      return null;
    }
  } catch (error) {
    console.log('❌ NGSP Token fetch failed:', error.message);
    
    if (error.isSSLError) {
      console.log('🔐 SSL Certificate issue detected');
      console.log('💡 Try running: openNGSPCertificateAcceptance()');
    }
    
    return null;
  }
};

/**
 * Test NGSP API call
 */
export const testNGSPAPI = async (token = null) => {
  console.log('🧪 Testing NGSP API Call...');
  
  if (!token) {
    console.log('🔄 Fetching token first...');
    token = await testNGSPToken();
    if (!token) {
      console.log('❌ Cannot test API without token');
      return false;
    }
  }
  
  const testRequestBody = {
    maTinh: "51",
    maDinhDanhHoSo: ["TEST_DOSSIER_CODE"],
    noiDangKy: "Test Location",
    module: "LTKT"
  };
  
  try {
    const response = await callNGSPAPI(
      '/Lienthonghotich/1.0/nhanHoSoDKHT', 
      testRequestBody, 
      token
    );
    
    console.log('✅ NGSP API call successful');
    console.log('Response status:', response.status);
    console.log('Response data preview:', JSON.stringify(response.data).substring(0, 100) + '...');
    return true;
  } catch (error) {
    console.log('❌ NGSP API call failed:', error.message);
    
    if (error.isSSLError) {
      console.log('🔐 SSL Certificate issue detected');
      console.log('💡 Try running: openNGSPCertificateAcceptance()');
    }
    
    return false;
  }
};

/**
 * Test SSL certificate acceptance
 */
export const testSSLAcceptance = () => {
  console.log('🧪 Testing SSL Certificate Acceptance...');
  console.log('🔐 Opening certificate acceptance page...');
  
  try {
    openNGSPCertificateAcceptance();
    console.log('✅ Certificate acceptance page opened');
    console.log('💡 Please accept the certificate in the new tab, then run tests again');
    return true;
  } catch (error) {
    console.log('❌ Failed to open certificate acceptance page:', error.message);
    return false;
  }
};

/**
 * Run comprehensive NGSP tests
 */
export const runNGSPTests = async () => {
  console.log('🚀 Starting NGSP Configuration Tests...');
  console.log('=====================================');
  
  // Test 1: Configuration check
  console.log('📋 Checking configuration...');
  if (!TOKEN_CONFIG.NGSP.username || !TOKEN_CONFIG.NGSP.password) {
    console.log('❌ NGSP credentials not configured');
    return false;
  }
  console.log('✅ NGSP credentials configured');
  console.log('Endpoint HTTPS:', TOKEN_CONFIG.NGSP.endpoint);
  console.log('Endpoint HTTP:', TOKEN_CONFIG.NGSP.endpointHttp);
  console.log('Timeout:', TOKEN_CONFIG.NGSP.timeout);
  
  // Test 2: Token fetch
  console.log('\n🔑 Testing token fetch...');
  const token = await testNGSPToken();
  
  if (!token) {
    console.log('\n🔐 Token fetch failed. Testing SSL certificate acceptance...');
    testSSLAcceptance();
    console.log('\n💡 After accepting certificate, run tests again');
    return false;
  }
  
  // Test 3: API call
  console.log('\n📡 Testing API call...');
  const apiSuccess = await testNGSPAPI(token);
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  console.log('Configuration:', '✅ OK');
  console.log('Token Fetch:', token ? '✅ OK' : '❌ FAILED');
  console.log('API Call:', apiSuccess ? '✅ OK' : '❌ FAILED');
  
  if (token && apiSuccess) {
    console.log('\n🎉 All tests passed! NGSP configuration is working correctly.');
    return true;
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs above for details.');
    console.log('\n💡 Common solutions:');
    console.log('1. Accept SSL certificate: openNGSPCertificateAcceptance()');
    console.log('2. Check VPN connection');
    console.log('3. Verify api.ngsp.gov.vn accessibility');
    console.log('4. Check browser SSL settings');
    return false;
  }
};

/**
 * Quick test function for console usage
 */
export const quickTest = async () => {
  console.log('⚡ Quick NGSP Test...');
  const token = await testNGSPToken();
  if (token) {
    await testNGSPAPI(token);
  } else {
    testSSLAcceptance();
  }
};

// Export for console usage
if (typeof window !== 'undefined') {
  window.testNGSP = {
    runTests: runNGSPTests,
    quickTest: quickTest,
    testToken: testNGSPToken,
    testAPI: testNGSPAPI,
    acceptSSL: testSSLAcceptance
  };
  
  console.log('🧪 NGSP Test utilities loaded!');
  console.log('Usage:');
  console.log('- window.testNGSP.runTests() - Run all tests');
  console.log('- window.testNGSP.quickTest() - Quick test');
  console.log('- window.testNGSP.acceptSSL() - Open SSL acceptance page');
}

export default {
  runNGSPTests,
  quickTest,
  testNGSPToken,
  testNGSPAPI,
  testSSLAcceptance
};
