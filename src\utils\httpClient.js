// httpClient.js - HTTP client configuration with SSL bypass for NGSP API
import axios from 'axios';
import { TOKEN_CONFIG } from '../config.js';

/**
 * Create axios instance with SSL bypass configuration for NGSP API
 * Note: SSL bypass is limited in browser environment
 */
export const createNGSPHttpClient = () => {
  const client = axios.create({
    timeout: TOKEN_CONFIG.NGSP.timeout || 15000,
    // SSL bypass configuration (limited in browser)
    httpsAgent: false,
    validateStatus: () => true, // Accept any status code for manual handling
    maxRedirects: 5,
    headers: {
      'Content-Type': 'application/json',
    }
  });

  // Request interceptor for logging
  client.interceptors.request.use(
    (config) => {
      console.log(`🚀 NGSP API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    },
    (error) => {
      console.error('❌ NGSP Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for logging and error handling
  client.interceptors.response.use(
    (response) => {
      console.log(`✅ NGSP API Response: ${response.status} ${response.statusText}`);
      return response;
    },
    (error) => {
      console.error('❌ NGSP Response Error:', error.message);
      
      // Check if it's an SSL/Certificate error
      if (error.code === 'CERT_UNTRUSTED' || 
          error.code === 'UNABLE_TO_VERIFY_LEAF_SIGNATURE' ||
          error.message.includes('certificate') ||
          error.message.includes('SSL') ||
          error.message.includes('HTTPS')) {
        
        const sslError = new Error('SSL Certificate Error - Cần accept certificate cho api.ngsp.gov.vn');
        sslError.isSSLError = true;
        sslError.originalError = error;
        throw sslError;
      }
      
      return Promise.reject(error);
    }
  );

  return client;
};

/**
 * Make NGSP API call with HTTPS/HTTP fallback
 * @param {string} endpoint - API endpoint path
 * @param {object} data - Request data
 * @param {string} token - Bearer token
 * @param {object} options - Additional axios options
 */
export const callNGSPAPI = async (endpoint, data, token, options = {}) => {
  const client = createNGSPHttpClient();
  
  // Try HTTPS first, then HTTP fallback
  const baseUrls = [
    'https://api.ngsp.gov.vn', // HTTPS with SSL bypass
    'http://api.ngsp.gov.vn'   // HTTP fallback
  ];

  for (let i = 0; i < baseUrls.length; i++) {
    const baseUrl = baseUrls[i];
    const protocol = baseUrl.startsWith('https') ? 'HTTPS' : 'HTTP';
    const fullUrl = `${baseUrl}${endpoint}`;
    
    try {
      console.log(`🔄 Trying NGSP API via ${protocol}: ${fullUrl}`);
      
      const response = await client.post(fullUrl, data, {
        ...options,
        headers: {
          ...client.defaults.headers,
          ...options.headers,
          Authorization: `Bearer ${token}`,
        }
      });
      
      console.log(`✅ NGSP API call via ${protocol} successful`);
      return response;
      
    } catch (error) {
      console.log(`❌ ${protocol} attempt failed:`, error.message);
      
      // If this is the last URL, throw the error
      if (i === baseUrls.length - 1) {
        throw error;
      }
    }
  }
};

/**
 * Fetch NGSP token with HTTPS/HTTP fallback
 * @param {string} username - NGSP username
 * @param {string} password - NGSP password
 */
export const fetchNGSPToken = async (username, password) => {
  const client = createNGSPHttpClient();
  
  // Create basic auth header
  const credentials = btoa(`${username}:${password}`);
  const authHeader = `Basic ${credentials}`;
  
  // Prepare form data
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  
  const requestConfig = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': authHeader
    }
  };

  // Try HTTPS first, then HTTP fallback
  const endpoints = [
    { url: TOKEN_CONFIG.NGSP.endpoint, type: 'HTTPS' },
    { url: TOKEN_CONFIG.NGSP.endpointHttp || TOKEN_CONFIG.NGSP.endpoint.replace('https://', 'http://'), type: 'HTTP' }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`🔄 Trying NGSP token fetch via ${endpoint.type}: ${endpoint.url}`);
      
      const response = await client.post(endpoint.url, params, requestConfig);
      
      if (response.data && response.data.access_token) {
        console.log(`✅ NGSP token fetched via ${endpoint.type}`);
        return response.data.access_token;
      }
      
    } catch (error) {
      console.log(`❌ ${endpoint.type} token fetch failed:`, error.message);
      
      // If this is the last endpoint, throw the error
      if (endpoint === endpoints[endpoints.length - 1]) {
        throw error;
      }
    }
  }
  
  throw new Error('Failed to fetch NGSP token from all endpoints');
};

/**
 * SSL Certificate acceptance helper
 */
export const openNGSPCertificateAcceptance = () => {
  window.open('https://api.ngsp.gov.vn/token', '_blank');
  console.log('🔐 Opened NGSP certificate acceptance page');
};

export default {
  createNGSPHttpClient,
  callNGSPAPI,
  fetchNGSPToken,
  openNGSPCertificateAcceptance
};
